<?php

namespace Modules\NavigationMenu\Database\Seeds;

use CodeIgniter\Database\Seeder;

class NavigationMenuSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'id' => 1,
                'parent_id' => null,
                'title' => 'Dashboard',
                'url' => '/admin',
                'icon' => 'ti ti-home',
                'target' => '_self',
                'sort_order' => 1,
                'is_active' => 1,
                'permission' => null,
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 2,
                'parent_id' => null,
                'title' => 'Interface',
                'url' => null,
                'icon' => 'ti ti-package',
                'target' => '_self',
                'sort_order' => 2,
                'is_active' => 1,
                'permission' => null,
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 3,
                'parent_id' => 2,
                'title' => 'Navigation Menu',
                'url' => '/admin/navigation',
                'icon' => 'ti ti-menu-2',
                'target' => '_self',
                'sort_order' => 1,
                'is_active' => 1,
                'permission' => null,
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 4,
                'parent_id' => null,
                'title' => 'Users',
                'url' => null,
                'icon' => 'ti ti-users',
                'target' => '_self',
                'sort_order' => 3,
                'is_active' => 1,
                'permission' => null,
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 5,
                'parent_id' => 4,
                'title' => 'All Users',
                'url' => '/admin/users',
                'icon' => 'ti ti-list',
                'target' => '_self',
                'sort_order' => 1,
                'is_active' => 1,
                'permission' => 'users.view',
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 6,
                'parent_id' => 4,
                'title' => 'Add User',
                'url' => '/admin/users/create',
                'icon' => 'ti ti-user-plus',
                'target' => '_self',
                'sort_order' => 2,
                'is_active' => 1,
                'permission' => 'users.create',
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 7,
                'parent_id' => null,
                'title' => 'Settings',
                'url' => '/admin/settings',
                'icon' => 'ti ti-settings',
                'target' => '_self',
                'sort_order' => 4,
                'is_active' => 1,
                'permission' => 'settings.manage',
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 8,
                'parent_id' => null,
                'title' => 'Reports',
                'url' => null,
                'icon' => 'ti ti-chart-bar',
                'target' => '_self',
                'sort_order' => 5,
                'is_active' => 1,
                'permission' => null,
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 9,
                'parent_id' => 8,
                'title' => 'Sales Report',
                'url' => '/admin/reports/sales',
                'icon' => 'ti ti-chart-line',
                'target' => '_self',
                'sort_order' => 1,
                'is_active' => 1,
                'permission' => 'reports.sales',
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 10,
                'parent_id' => 8,
                'title' => 'User Activity',
                'url' => '/admin/reports/activity',
                'icon' => 'ti ti-activity',
                'target' => '_self',
                'sort_order' => 2,
                'is_active' => 1,
                'permission' => 'reports.activity',
                'css_class' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Using Query Builder
        $this->db->table('navigation_menus')->insertBatch($data);
    }
}
