<?php

namespace Modules\CitemDbManagement\Config;


$routes->group('admin',function($routes){
    $routes->group('citem',['namespace'=>'Modules\CitemDbManagement\Controllers'],function($routes){
        $routes->GET('/', 'VpsController::index', ['as' => 'citem.index']);


        //Ajax endpoints
        $routes->GET('vps/datatable', 'VpsController::datatable', ['as' => 'citem.vps.datatable']);
        $routes->POST('vps/datatable', 'VpsController::datatable', ['as' => 'citem.vps.datatable']);
        
    });
});
