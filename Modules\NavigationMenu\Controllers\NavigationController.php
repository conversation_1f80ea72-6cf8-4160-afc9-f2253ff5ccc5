<?php

namespace Modules\NavigationMenu\Controllers;

use App\Controllers\BaseController;
use Modules\NavigationMenu\Models\NavigationMenuModel;
use Modules\RoleManagement\Models\RoleModel;
use Hermawan\DataTables\DataTable;

class NavigationController extends BaseController
{
    protected $navigationModel;
    protected $roleModel;

    public function __construct()
    {
        $this->navigationModel = new NavigationMenuModel();
        $this->roleModel = new RoleModel();
    }

    /**
     * Display navigation menu list
     */
    public function index()
    {
        // Check permissions
        if (!hasPermission('menu.view')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to view menus.');
        }

        $data = [
            'title' => 'Navigation Menu Management',
            'page_title' => 'Navigation Menu Management',
            'statistics' => [
                'total' => $this->navigationModel->countAllResults(),
                'active' => $this->navigationModel->where('is_active', 1)->countAllResults(),
                'inactive' => $this->navigationModel->where('is_active', 0)->countAllResults(),
                'root_menus' => $this->navigationModel->where('parent_id IS NULL')->countAllResults(),
            ]
        ];

        return view('Modules\NavigationMenu\Views\index', $data);
    }

    /**
     * DataTables AJAX endpoint using Hermawan DataTables
     */
    public function datatable()
    {
        if (!hasPermission('menu.view')) {
            return $this->response->setJSON(['error' => 'Permission denied']);
        }

        $builder = $this->navigationModel->db->table('navigation_menus');

        return DataTable::of($builder)
            ->addNumbering('DT_RowIndex')
            ->add('checkbox', function($row) {
                return hasPermission('menu.manage') ? '<input type="checkbox" class="select-item form-check-input" value="' . $row->id . '">' : '';
            })
            ->edit('title', function ($row) {
                $indent = '';
                if ($row->parent_id) {
                    // Add indentation for child menus
                    $level = $this->getMenuLevel($row->id);
                    $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
                }

                $icon = $row->icon ? '<i class="' . $row->icon . ' me-2"></i>' : '';
                return $indent . $icon . esc($row->title);
            })
            ->edit('url', function ($row) {
                return $row->url ? '<code class="text-muted">' . esc($row->url) . '</code>' : '<span class="text-muted">No URL</span>';
            })
            ->add('status_badge', function($row) {
                return $row->is_active
                    ? '<span class="badge bg-success">Active</span>'
                    : '<span class="badge bg-danger">Inactive</span>';
            })
            ->add('created_date', function($row) {
                return date('M d, Y', strtotime($row->created_at));
            })
            ->add('actions', function($row) {
                $actions = '';
                if (hasPermission('menu.view')) {
                    $actions .= '<a href="javascript:void(0)" onclick="viewMenu(' . $row->id . ')" class="btn btn-sm btn-outline-info me-1" title="View"><i class="ti ti-eye"></i></a>';
                }
                if (hasPermission('menu.manage')) {
                    $actions .= '<a href="' . site_url('admin/navigation/edit/' . $row->id) . '" class="btn btn-sm btn-outline-primary me-1" title="Edit"><i class="ti ti-edit"></i></a>';
                    $actions .= '<a href="javascript:void(0)" onclick="deleteMenu(' . $row->id . ')" class="btn btn-sm btn-outline-danger" title="Delete"><i class="ti ti-trash"></i></a>';
                }
                return $actions;
            })
            ->toJson();
    }

    /**
     * Get menu level for indentation
     */
    private function getMenuLevel($menuId, $level = 0)
    {
        $menu = $this->navigationModel->find($menuId);
        if ($menu && $menu['parent_id']) {
            return $this->getMenuLevel($menu['parent_id'], $level + 1);
        }
        return $level;
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = [
            'title' => 'Create Navigation Menu',
            'parentMenus' => $this->navigationModel->getAllMenus(true)
        ];

        return view('Modules\NavigationMenu\Views\create', $data);
    }

    /**
     * Store new navigation menu
     */
    public function store()
    {
        $rules = [
            'title' => 'required|max_length[255]',
            'url' => 'permit_empty|max_length[500]',
            'icon' => 'permit_empty|max_length[100]',
            'target' => 'permit_empty|in_list[_self,_blank,_parent,_top]',
            'sort_order' => 'permit_empty|integer',
            'is_active' => 'permit_empty|in_list[0,1]',
            'permission' => 'permit_empty|max_length[100]',
            'css_class' => 'permit_empty|max_length[100]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'title' => $this->request->getPost('title'),
            'url' => $this->request->getPost('url'),
            'icon' => $this->request->getPost('icon'),
            'target' => $this->request->getPost('target') ?: '_self',
            'sort_order' => $this->request->getPost('sort_order') ?: 
                          $this->navigationModel->getMaxSortOrder($this->request->getPost('parent_id')) + 1,
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'permission' => $this->request->getPost('permission'),
            'css_class' => $this->request->getPost('css_class'),
        ];

        if ($this->navigationModel->insert($data)) {
            return redirect()->to('/admin/navigation')->with('success', 'Navigation menu created successfully.');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to create navigation menu.');
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $menu = $this->navigationModel->find($id);
        
        if (!$menu) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Navigation menu not found.');
        }

        $data = [
            'title' => 'Edit Navigation Menu',
            'menu' => $menu,
            'parentMenus' => $this->navigationModel->getAllMenus(true)
        ];

        return view('Modules\NavigationMenu\Views\edit', $data);
    }

    /**
     * Update navigation menu
     */
    public function update($id)
    {
        $menu = $this->navigationModel->find($id);
        
        if (!$menu) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Navigation menu not found.');
        }

        $rules = [
            'title' => 'required|max_length[255]',
            'url' => 'permit_empty|max_length[500]',
            'icon' => 'permit_empty|max_length[100]',
            'target' => 'permit_empty|in_list[_self,_blank,_parent,_top]',
            'sort_order' => 'permit_empty|integer',
            'is_active' => 'permit_empty|in_list[0,1]',
            'permission' => 'permit_empty|max_length[100]',
            'css_class' => 'permit_empty|max_length[100]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'title' => $this->request->getPost('title'),
            'url' => $this->request->getPost('url'),
            'icon' => $this->request->getPost('icon'),
            'target' => $this->request->getPost('target') ?: '_self',
            'sort_order' => $this->request->getPost('sort_order'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'permission' => $this->request->getPost('permission'),
            'css_class' => $this->request->getPost('css_class'),
        ];

        if ($this->navigationModel->update($id, $data)) {
            return redirect()->to('/admin/navigation')->with('success', 'Navigation menu updated successfully.');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update navigation menu.');
    }

    /**
     * Delete navigation menu
     */
    public function delete($id)
    {
        $menu = $this->navigationModel->find($id);
        
        if (!$menu) {
            return $this->response->setJSON(['success' => false, 'message' => 'Navigation menu not found.']);
        }

        // Check if menu has children
        if ($this->navigationModel->hasChildren($id)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete menu with sub-items.']);
        }

        if ($this->navigationModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Navigation menu deleted successfully.']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete navigation menu.']);
    }

    /**
     * Reorder navigation menus
     */
    public function reorder()
    {
        $items = $this->request->getJSON(true);
        
        if ($this->navigationModel->updateSortOrder($items)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu order updated successfully.']);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to update menu order.']);
    }
}
