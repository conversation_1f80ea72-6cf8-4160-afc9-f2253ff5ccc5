<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">User Management</li>
<?= $this->endSection() ?>


<?= $this->section('header'); ?>
  <!-- DataTables -->
  <!-- DataTables -->
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css"); ?>">
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css"); ?>">
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css"); ?>">

<style>
      .max-width-column-150 {
      white-space: normal !important;
      word-wrap: break-word !important;
      max-width: 150px;
      text-align: center!important;
/*      overflow: hidden;*/
/*      text-overflow: ellipsis;*/
    }
    .max-width-column-350 {
      white-space: normal !important;
      word-wrap: break-word !important;
      max-width: 350px;
      text-align: center!important;
/*      overflow: hidden;*/
/*      text-overflow: ellipsis;*/
    }
</style>
<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title">PROFILE</h5>
 
                <p class="card-text">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="card">
                        <div class="card-header">
                          <h3 class="card-title">Count</h3>
                        </div>
                        <div class="card-body">
                          <p class="card-text">Total FamePlus: <?= $fameplus_count ?></p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- <div class="container" style="max-width:1200px"> -->
                  <table id="profile_table" class=" table-bordered table-striped display compact cell-border" style="width:100%">
                    <thead>
                      <tr>
                        <!-- <th>#</th> -->
                        <th>fplus</th>
                        <th>fplus status</th>
                        <th>citem status</th>
                        <th>citem</th>
                        <th class="max-width-column-20">fair codes</th>
                        <th>remarks</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($data as $row) : ?>
                        <?php 
                        $fair_codes = $row['fair_codes']??'';
                        $fair_codes = str_replace(',', '<br>', $fair_codes);
                        $validation_status = $row['validation_status']??'';
                        $co_name = $row['co_name']??'';
                        $citem_email = $row['citem_email']??'';
                        ?>  
                        
                        <tr>
                          <td><?= $row['fplus_email'].'<br>'.$row['fplus_company'] ?></td>
                          <td><?= $row['fplus_status'] ?></td>
                          <td><?= $citem_email.'<br>'.$co_name ?></td>
                          <td><?= ($row['fplus_status']!=$validation_status)? '<span style="color:red;">ALERT</span>' : '' ?>
                          <?= $row['validation_status']??'' ?>
                        
                        </td>
                          <td><?= $fair_codes??'' ?></td>
                          <td>
                            <?php if (isset($row['citem_email'])) : ?>
                              Have CITEMDB Record</br>
                              <?php if($row['fplus_id']==$row['fameplus_id']): ?>
                                FPLUSID MATCHED
                              <?php else: ?>
                                <span style="color:red;">ALERT FPLUSID UNMATCHED (<?=$row['fplus_id'].'|'.$row['fameplus_id']?>)</span>
                                
                              <?php endif; ?>
                            <?php else: ?>
                              <span style="color:red;">ALERT No CITEMDB Record</span>
                              
                            <?php endif; ?>
                          </td>
                          <td>

                            <div class="btn-group">
                              <button class="btn btn-sm btn-info post-btn" data-id="<?= $row['fplus_id'] ?>" title="API POST">
                                <i class="fas fa-upload"></i> POST
                              </button>
                              <!-- <button class="btn btn-sm btn-primary patch-btn" data-id="<?//= $row['ifexconn_id'] ?>" title="API PATCH">
                                <i class="fas fa-edit"></i> PATCH
                              </button> -->
                              <button class="btn btn-sm btn-warning validate-patch-btn" data-id="<?= $row['fplus_id'] ?>" title="Validate">
                                <i class="fas fa-check-circle"></i> VALIDATE
                              </button>
                            </div>

                          </td>
                        </tr>
                      <?php endforeach;?>

                    </tbody>
                  </table>
                <!-- </div> -->

                </p>
                <!-- <a href="#" class="card-link">Card link</a> -->
                <!-- <a href="#" class="card-link">Another link</a> -->
              </div>
            </div>
          </div>
        </div>
        <!-- /.row -->




<?= $this->endSection(); ?>

<?= $this->section("script") ?>
<!-- DataTables  & Plugins -->
<script src="<?php echo base_url("assets/plugins/datatables/jquery.dataTables.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/jszip/jszip.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/pdfmake/pdfmake.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/pdfmake/vfs_fonts.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js"); ?>"></script>
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>

<script>

$(document).ready(function() {
  var base_url = '<?= base_url(); ?>';

  //for the datatable listing
  var profileTable = $('#profile_table').DataTable({
      // paging: true,
      ordering: false,
      // responsive: true,
      // lengthChange: true,
      autoWidth: true,
      // search: { return: true },
      // serverSide: true,
      // ajax:{
      //   url: '<?php //echo site_url('/ifex/consolidated-data')?>',
      // },
      // columns: [
        // { data: '#', orderable: false},
          // { data: 'email',"className": "max-width-column-150" },
          // { data: 'co_name',"className": "max-width-column-150"},
          // { data: 'payload', "className": "max-width-column-350" },
          // { data: 'country', "className": "max-width-column-170" },
          // { data: 'cost' },
          // { data: 'updated_cost', "className": "sale-updates-cell" },
          // { data: 'status', "className": "max-width-column-80" },
          // { data: 'updated_status', "className": "sale-updates-cell"},
        // { data: 'original_date' },
        // { data: 'updated_date' },
          // { data: 'action' },
      // ],
    });


  $('#profile_table').on('click','.post-btn',function(){
    event.preventDefault();
    //get the data-id and email
    var id = $(this).data('id');
    var email = $(this).closest('tr').find('td:first').text();
    // var company = $(this).closest('tr').find('td:nth-child(2)').text();
    var method = 'post';
    console.log("ID: " + id);
    console.log("Email: " + email);
    
    // Disable the button and show processing state
    var $btn = $(this);
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
    
    $.ajax({
        dataType: 'json',
        type:'post',
        url: '<?= site_url('/ifex/guzzlehttp')?>',
        data: {email: email,method:method},
        cache: false,
        beforeSend: function(){
                // $('.loader').show();
            },
        success: function(response) {
          console.log(response);
          if (response.status=='success') {
            Swal.fire({
              title: "Saved!",
              text: response.status+': '+response.message,
              icon: "success",
            });
          // salesTable.draw();
          // profileTable.ajax.reload();
          // Update the table row or reload the page
        } else {
          Swal.fire({
              title: response.status,
              text: response.status+': '+response.message,
              icon: "error",
            });
        }
      },
      error: function(xhr, status, error) {
        console.log('error');
      },
      complete: function() {
        // Re-enable the button and restore original text
        $btn.prop('disabled', false).html('<i class="fas fa-upload"></i> POST');
      }
    });
  });

  $('#profile_table').on('click','.patch-btn',function(){
    event.preventDefault();

    var targetRow = $(this).closest('tr');
    var rowData = $('#profile_table').DataTable().row(targetRow).data();
    var email = rowData.email;
    var method = 'patch';
    var payload = rowData.payload;
    console.log("Email: " + email);
    console.log("Payload: " + payload);
    alert("Email: " + email + "\nPayload: " + payload);
    $.ajax({
        dataType: 'json',
        type:'post',
        url: '<?= site_url('/ifex/guzzlehttp')?>',
        data: {email: email,payload:payload,method:method},
        cache: false,
        beforeSend: function(){
                // $('.loader').show();
            },
        success: function(response) {
          if (response.status=='success') {
            Swal.fire({
              title: "Saved!",
              text: response.status+': '+response.message,
              icon: "success",
            });
          // salesTable.draw();
          // inquiryTable.ajax.reload();
          // $('#'+modalId).modal('hide');
          // activateTab('inquiry-content-tab');
          // Update the table row or reload the page
        } else {
          Swal.fire({
              title: response.status,
              text: response.status+': '+response.message,
              icon: "success",
            });
        }
      },
      error: function(xhr, status, error) {
        console.error('error');
      }
    });
  });

  $('#profile_table').on('click','.validate-patch-btn',function(){
    event.preventDefault();
    //debug
    console.log('validate patch');

    var id = $(this).data('id');
    var email = $(this).closest('tr').find('td:first').text();
    var status = $(this).closest('tr').find('td:nth-child(3)').text();
    var method = 'validatepatch';

    var $btn = $(this);
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

    $.ajax({
        dataType: 'json',
        type:'post',
        url: '<?= site_url('/ifex/guzzlehttp')?>',
        data: {email: email,status:status,method:method},
        cache: false,
        beforeSend: function(){
                // $('.loader').show();
            },
        success: function(response) {
          if (response.status=='success') {
            Swal.fire({
              title: response.status,
              text: response.status+': '+response.message,
              icon: "success",
            });
          // salesTable.draw();
          // inquiryTable.ajax.reload();
          // $('#'+modalId).modal('hide');
          // activateTab('inquiry-content-tab');
          // Update the table row or reload the page
        } else {
          Swal.fire({
              title: response.status,
              text: response.status+': '+response.message,
              icon: "error",
            });
        }
      },
      error: function(xhr, status, error) {
        console.error('error');
      },
      complete: function() {
        // Re-enable the button and restore original text
        $btn.prop('disabled', false).html('<i class="fas fa-check-circle"></i> VALIDATE');
      }
    });
  });




});

</script>

<?= $this->endSection(); ?>
