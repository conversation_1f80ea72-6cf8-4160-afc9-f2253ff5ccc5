<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">JSON to List</li>
<?= $this->endSection() ?>




<?= $this->section('header'); ?>
  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdn.datatables.net/2.1.3/css/dataTables.dataTables.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/3.0.2/css/responsive.dataTables.css">

<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

        <div class="row">
          <div class="col-12">
            <?php if($_POST):?>
                <div class="alert alert-success">JSON Data Converted to List</div>
                <button class="btn btn-primary" onclick="copyToClipboard()">Copy to Clipboard</button>
                <div id="textToCopy">


                    <?php 
                        if($_POST){
                            $myAr = json_decode($_POST['json_data'],true);
                            foreach($myAr as $key=>$value){
                                echo $key.':'.$value.'<br>';
                            }
                            echo '<br><br>';
                        }
                        ?>





                </div>
                
            <?php endif; ?>
            
          </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"></h5>
                        <p class="card-text">
                            <form role="form" action="<?= route_to('apitool.json2list') ?>" method="post">
                                <div class="form-group">
                                    <label>JSON Data</label>
                                    <textarea class="form-control" rows="10" placeholder="Enter ..." name="json_data"></textarea>

                            </div>


                            <div class="row no-print">
                            <div class="col-xs-12 col-md-12">
                                <!-- <a href="invoice-print.html" target="_blank" class="btn btn-default"><i class="fa fa-print"></i> Print</a> -->
                                <button type="submit" class="btn btn-primary pull-right btn-block">
                                <i class="<i class="fa-solid fa-square-arrow-up-right"></i> SUBMIT
                                </button>
                            </div>
                            </div>


                        </div>
                    </div>

                    <br>
                    <br>
                            </form>
                <!-- </div> -->
                  




                </p>
                <!-- <a href="#" class="card-link">Card link</a> -->
                <!-- <a href="#" class="card-link">Another link</a> -->
              </div>
            </div>
          </div>
        </div>
        <!-- /.row -->

<?= $this->endSection(); ?>


<?= $this->section("script") ?>
<!-- DataTables  & Plugins -->
<script src="https://cdn.datatables.net/2.1.3/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/dataTables.responsive.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/responsive.dataTables.js"></script>
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>

<script>

$(document).ready(function() {
  var base_url = '<?= base_url(); ?>';

  //for the sales datatable listing
  var profileTable = $('#profile_table').DataTable({
      paging: true,
      ordering: false,
      lengthChange: true,
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      search: { return: true },
      serverSide: true,
      ajax:{
        url: '<?= site_url('/api/getProfiles')?>',
      },
      columns: [
        { data: '#', orderable: false},
          { data: 'email' },
          { data: 'co_name', "className": "max-width-column-170"},
          { data: 'cont_per_fn', "className": "max-width-column-170" },
          // { data: 'country', "className": "max-width-column-170" },
          // { data: 'cost' },
          // { data: 'updated_cost', "className": "sale-updates-cell" },
          // { data: 'status', "className": "max-width-column-80" },
          // { data: 'updated_status', "className": "sale-updates-cell"},
        // { data: 'original_date' },
        // { data: 'updated_date' },
          { data: 'action' },
      ],
    });


});

function copyToClipboard() {
    const text = document.getElementById("textToCopy").innerText;
    navigator.clipboard.writeText(text).then(() => {
        alert("Copied to clipboard!");
    }).catch(err => {
        console.error("Failed to copy: ", err);
    });
}

</script>

<?= $this->endSection(); ?>