<?php

namespace Modules\NavigationMenu\Models;

use CodeIgniter\Model;

class NavigationMenuModel extends Model
{
    protected $table            = 'navigation_menus';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'parent_id', 'title', 'url', 'icon', 'target', 
        'sort_order', 'is_active', 'permission', 'css_class'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'title'      => 'required|max_length[255]',
        'url'        => 'permit_empty|max_length[500]',
        'icon'       => 'permit_empty|max_length[100]',
        'target'     => 'permit_empty|in_list[_self,_blank,_parent,_top]',
        'sort_order' => 'permit_empty|integer',
        'is_active'  => 'permit_empty|in_list[0,1]',
        'permission' => 'permit_empty|max_length[100]',
        'css_class'  => 'permit_empty|max_length[100]',
    ];

    protected $validationMessages = [
        'title' => [
            'required' => 'Menu title is required.',
            'max_length' => 'Menu title cannot exceed 255 characters.'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get all menu items ordered by sort_order
     */
    public function getAllMenus($activeOnly = false)
    {
        $builder = $this->builder();
        
        if ($activeOnly) {
            $builder->where('is_active', 1);
        }
        
        return $builder->orderBy('sort_order', 'ASC')->get()->getResultArray();
    }

    /**
     * Get menu tree structure
     */
    public function getMenuTree($parentId = null, $activeOnly = false)
    {
        $builder = $this->builder();
        
        if ($activeOnly) {
            $builder->where('is_active', 1);
        }
        
        $builder->where('parent_id', $parentId)
                ->orderBy('sort_order', 'ASC');
        
        $menus = $builder->get()->getResultArray();
        
        foreach ($menus as &$menu) {
            $menu['children'] = $this->getMenuTree($menu['id'], $activeOnly);
        }
        
        return $menus;
    }

    /**
     * Get menu breadcrumb
     */
    public function getBreadcrumb($menuId)
    {
        $breadcrumb = [];
        $menu = $this->find($menuId);
        
        while ($menu) {
            array_unshift($breadcrumb, $menu);
            $menu = $menu['parent_id'] ? $this->find($menu['parent_id']) : null;
        }
        
        return $breadcrumb;
    }

    /**
     * Update sort order for multiple items
     */
    public function updateSortOrder($items)
    {
        $this->db->transStart();
        
        foreach ($items as $item) {
            $this->update($item['id'], ['sort_order' => $item['sort_order']]);
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * Get maximum sort order for a parent
     */
    public function getMaxSortOrder($parentId = null)
    {
        $builder = $this->builder();
        $builder->selectMax('sort_order', 'max_order')
                ->where('parent_id', $parentId);
        
        $result = $builder->get()->getRowArray();
        
        return $result['max_order'] ?? 0;
    }

    /**
     * Check if menu has children
     */
    public function hasChildren($menuId)
    {
        return $this->where('parent_id', $menuId)->countAllResults() > 0;
    }
}
