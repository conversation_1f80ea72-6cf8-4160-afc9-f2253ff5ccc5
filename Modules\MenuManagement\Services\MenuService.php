<?php

namespace Modules\MenuManagement\Services;

use Modules\MenuManagement\Models\MenuModel;
use Modules\Authentication\Services\PermissionService;
use Modules\UserManagement\Models\UserModel;
use Modules\RoleManagement\Models\UserRoleModel;

class MenuService
{
    protected $menuModel;
    protected $permissionService;
    protected $userModel;
    protected $userRoleModel;
    protected $cache;
    protected $session;

    public function __construct()
    {
        $this->menuModel = new MenuModel();
        $this->userRoleModel = new UserRoleModel();
        $this->permissionService = new PermissionService();
        $this->userModel = new UserModel();
        $this->cache = \Config\Services::cache();
        $this->session = \Config\Services::session();
    }

    /**
     * Get user-accessible menus with hybrid caching (session + cache) - ROLE-BASED
     */
    public function getUserMenus($userId = null)
    {
        if (!$userId) {
            $userId = $this->session->get('user_id');
        }

        if (!$userId) {
            return [];
        }

        // Get user roles for cache key generation
        $userRoles = $this->userRoleModel->getUserRolesList($userId);
        $userRoleIds = array_column($userRoles, 'role_id');

        // Create ROLE-BASED cache keys (more efficient - multiple users with same roles share cache)
        sort($userRoleIds); // Ensure consistent ordering for cache key
        $cacheKeyData = [
            'role_ids' => $userRoleIds,  // Only roles, not user_id
            'version' => '1.0' // Increment this when menu structure changes
        ];
        $cacheKey = 'role_menus_' . md5(serialize($cacheKeyData));
        $sessionKey = 'user_menus_' . $userId; // Session is still user-specific for convenience

        // 1. Try session first (fastest) - user-specific for convenience
        $menus = $this->session->get($sessionKey);
        if ($menus !== null) {
            return $menus;
        }

        // 2. Try role-based cache second (fast) - shared across users with same roles
        $menus = $this->cache->get($cacheKey);
        if ($menus !== null) {
            // Store in user session for even faster access next time
            $this->session->set($sessionKey, $menus);
            return $menus;
        }

        // 3. Generate from database (slowest)
        $userPermissions = []; // Legacy system disabled
        $menus = $this->menuModel->getUserMenus($userPermissions, $userRoleIds);

        // Store in both role-based cache and user session
        if (!empty($menus)) {
            // Cache by roles for 1 hour (shared across users with same roles)
            $this->cache->save($cacheKey, $menus, 3600);
            // Session until browser closes (user-specific for convenience)
            $this->session->set($sessionKey, $menus);
        }

        return $menus;
    }

    /**
     * Render menu HTML for sidebar
     */
    public function renderSidebarMenu($userId = null)
    {
        $menus = $this->getUserMenus($userId);
        return $this->buildMenuHtml($menus);
    }

    /**
     * Build HTML for menu items
     */
    private function buildMenuHtml($menus, $level = 0)
    {
        if (empty($menus)) {
            return '';
        }

        $html = '';

        foreach ($menus as $menu) {
            $hasChildren = isset($menu['children']) && !empty($menu['children']);
            $isActive = $this->isMenuActive($menu['url']);
            $hasActiveChild = $hasChildren ? $this->hasActiveChild($menu['children']) : false;
            $shouldBeOpen = $isActive || $hasActiveChild;

            // Determine nav-item classes
            $navItemClasses = 'nav-item';
            if ($hasChildren) {
                $navItemClasses .= ' has-treeview';
                if ($shouldBeOpen) {
                    $navItemClasses .= ' menu-open';
                }
            }

            $html .= '<li class="' . $navItemClasses . '">';

            if ($hasChildren) {
                // Parent menu with children
                $navLinkClasses = 'nav-link';
                if ($shouldBeOpen) {
                    $navLinkClasses .= ' active';
                }

                $html .= '<a href="#" class="' . $navLinkClasses . '">';
                $html .= '<i class="nav-icon ' . ($menu['icon'] ?: 'fas fa-circle') . '"></i>';
                $html .= '<p>' . esc($menu['label']) . '<i class="right fas fa-angle-left"></i></p>';
                $html .= '</a>';

                // Add style to keep submenu open if it has active children
                $treeviewClasses = 'nav nav-treeview';
                $treeviewStyle = $shouldBeOpen ? ' style="display: block;"' : '';

                $html .= '<ul class="' . $treeviewClasses . '"' . $treeviewStyle . '>';
                $html .= $this->buildMenuHtml($menu['children'], $level + 1);
                $html .= '</ul>';
            } else {
                // Single menu item
                $url = $menu['url'] ? base_url($menu['url']) : '#';
                $navLinkClasses = 'nav-link';
                if ($isActive) {
                    $navLinkClasses .= ' active';
                }

                $html .= '<a href="' . $url . '" class="' . $navLinkClasses . '">';

                if ($level > 0) {
                    $html .= '<i class="far fa-circle nav-icon"></i>';
                } else {
                    $html .= '<i class="nav-icon ' . ($menu['icon'] ?: 'fas fa-circle') . '"></i>';
                }

                $html .= '<p>' . esc($menu['label']) . '</p>';
                $html .= '</a>';
            }

            $html .= '</li>';
        }

        return $html;
    }

    /**
     * Check if menu is currently active
     */
    private function isMenuActive($menuUrl)
    {
        if (!$menuUrl) {
            return false;
        }

        $currentUrl = uri_string();

        // Remove leading slash for comparison
        $menuUrl = ltrim($menuUrl, '/');
        $currentUrl = ltrim($currentUrl, '/');

        // Exact match
        if ($menuUrl === $currentUrl) {
            return true;
        }

        // Check if current URL starts with menu URL (for parent menus)
        if (strpos($currentUrl, $menuUrl) === 0) {
            return true;
        }

        return false;
    }

    /**
     * Check if any child menu is active (recursive)
     */
    private function hasActiveChild($children)
    {
        foreach ($children as $child) {
            // Check if this child is active
            if ($this->isMenuActive($child['url'])) {
                return true;
            }

            // Check if this child has active grandchildren
            if (isset($child['children']) && !empty($child['children'])) {
                if ($this->hasActiveChild($child['children'])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get breadcrumb trail for current page
     */
    public function getBreadcrumb($userId = null)
    {
        $currentUrl = uri_string();
        $menus = $this->getUserMenus($userId);
        
        return $this->findBreadcrumbPath($menus, $currentUrl);
    }

    /**
     * Find breadcrumb path recursively
     */
    private function findBreadcrumbPath($menus, $currentUrl, $path = [])
    {
        foreach ($menus as $menu) {
            $newPath = array_merge($path, [$menu]);
            
            // Check if this menu matches current URL
            if ($menu['url'] && $this->isMenuActive($menu['url'])) {
                return $newPath;
            }
            
            // Check children
            if (isset($menu['children']) && !empty($menu['children'])) {
                $childPath = $this->findBreadcrumbPath($menu['children'], $currentUrl, $newPath);
                if (!empty($childPath)) {
                    return $childPath;
                }
            }
        }
        
        return [];
    }

    /**
     * Clear menu caches for a specific user (session) and their roles (file cache)
     */
    public function clearUserMenuCache($userId)
    {
        // Clear user's session cache
        $sessionKey = 'user_menus_' . $userId;
        $this->session->remove($sessionKey);

        // Clear role-based file cache for this user's roles
        $userRoles = $this->userRoleModel->getUserRolesList($userId);
        $userRoleIds = array_column($userRoles, 'role_id');

        if (!empty($userRoleIds)) {
            sort($userRoleIds); // Ensure consistent ordering
            $cacheKeyData = [
                'role_ids' => $userRoleIds,
                'version' => '1.0'
            ];
            $cacheKey = 'role_menus_' . md5(serialize($cacheKeyData));
            $this->cache->delete($cacheKey);
        }
    }

    /**
     * Clear all menu caches (both session and role-based file cache)
     */
    public function clearAllMenuCache()
    {
        // Clear model cache
        $this->menuModel->clearMenuCache();

        // Clear all user menu sessions (user-specific)
        $sessionData = $this->session->get();
        if (is_array($sessionData)) {
            foreach (array_keys($sessionData) as $key) {
                if (strpos($key, 'user_menus_') === 0) {
                    $this->session->remove($key);
                }
            }
        }

        // Clear all role-based file caches
        // Since we use role-based caching, we need to clear all role_menus_* keys
        // Using clean() is the safest approach to ensure all role-based caches are cleared
        $this->cache->clean();

        // Also clear specific stats cache
        $this->cache->delete('menu_stats');
    }

    /**
     * Get menu statistics for dashboard
     */
    public function getMenuStats()
    {
        $cacheKey = 'menu_stats';
        $stats = $this->cache->get($cacheKey);
        
        if ($stats === null) {
            $stats = [
                'total_menus' => $this->menuModel->countAll(),
                'active_menus' => $this->menuModel->where('active', 1)->countAllResults(false),
                'parent_menus' => $this->menuModel->where('parent_id', null)->countAllResults(false),
                'child_menus' => $this->menuModel->where('parent_id IS NOT NULL', null, false)->countAllResults(false),
            ];
            
            // Cache for 2 hours instead of 1
            $this->cache->save($cacheKey, $stats, 7200);
        }
        
        return $stats;
    }

    /**
     * Validate menu hierarchy (prevent circular references)
     */
    public function validateMenuHierarchy($menuId, $parentId)
    {
        if (!$parentId) {
            return true; // No parent is always valid
        }
        
        if ($menuId == $parentId) {
            return false; // Menu cannot be its own parent
        }
        
        // Check if parent is a descendant of the menu
        return !$this->isDescendant($menuId, $parentId);
    }

    /**
     * Check if a menu is a descendant of another menu
     */
    private function isDescendant($ancestorId, $menuId)
    {
        $menu = $this->menuModel->find($menuId);
        
        if (!$menu || !$menu['parent_id']) {
            return false;
        }
        
        if ($menu['parent_id'] == $ancestorId) {
            return true;
        }
        
        return $this->isDescendant($ancestorId, $menu['parent_id']);
    }

    /**
     * Get menu tree for admin management (with edit capabilities)
     */
    public function getAdminMenuTree()
    {
        return $this->menuModel->getHierarchicalMenus();
    }

    /**
     * Reorder menus
     */
    public function reorderMenus($orders)
    {
        $result = $this->menuModel->updateSortOrders($orders);
        
        if ($result) {
            $this->clearAllMenuCache();
        }
        
        return $result;
    }
}
