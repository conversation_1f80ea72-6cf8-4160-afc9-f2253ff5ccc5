# Guzzle HTTP Module for CodeIgniter 4

A comprehensive HTTP client module for CodeIgniter 4 that wraps GuzzleHttp\Client with additional features like error handling, logging, configuration management, and helper functions.

## Features

- ✅ Easy-to-use wrapper for GuzzleHttp\Client
- ✅ Consistent error handling and logging
- ✅ Configurable timeouts, headers, and authentication
- ✅ Helper functions for quick HTTP operations
- ✅ Support for multiple endpoints and authentication presets
- ✅ File upload and download capabilities
- ✅ Environment-specific configuration
- ✅ Service container integration

## Installation

The module is already installed in your `Modules/GuzzleHttp` directory and configured in the autoloader.

## Quick Start

### Using the Service

```php
// In your controller
$guzzle = \Config\Services::guzzle();

// Simple GET request
$response = $guzzle->get('https://jsonplaceholder.typicode.com/posts/1');

if ($response['success']) {
    $data = $response['data'];
    // Process your data
}
```

### Using Helper Functions

```php
// Load the helper
helper('Modules\GuzzleHttp\Helpers\guzzle');

// Quick GET request
$response = http_get('https://jsonplaceholder.typicode.com/posts/1');

// Quick POST request
$response = http_post('https://jsonplaceholder.typicode.com/posts', [
    'title' => 'My Post',
    'body' => 'Post content',
    'userId' => 1
]);
```

## Configuration

### Basic Configuration

Edit `Modules/GuzzleHttp/Config/GuzzleHttp.php` to customize default settings:

```php
public $timeout = 30;
public $connectTimeout = 10;
public $verifySSL = true;
public $defaultHeaders = [
    'User-Agent' => 'MyApp/1.0',
    'Accept' => 'application/json'
];
```

### Environment-Specific Settings

```php
public $environments = [
    'development' => [
        'verifySSL' => false,
        'timeout' => 60
    ],
    'production' => [
        'verifySSL' => true,
        'timeout' => 30
    ]
];
```

### Endpoint Presets

Define commonly used APIs:

```php
public $endpoints = [
    'jsonplaceholder' => [
        'base_url' => 'https://jsonplaceholder.typicode.com',
        'timeout' => 15,
        'headers' => [
            'Accept' => 'application/json'
        ]
    ],
    'my_api' => [
        'base_url' => 'https://api.myservice.com/v1',
        'timeout' => 30,
        'headers' => [
            'Accept' => 'application/json',
            'X-API-Key' => env('MY_API_KEY')
        ]
    ]
];
```

### Authentication Presets

```php
public $authPresets = [
    'api_key' => [
        'type' => 'header',
        'header_name' => 'X-API-Key',
        'value' => env('API_KEY')
    ],
    'bearer_token' => [
        'type' => 'header',
        'header_name' => 'Authorization',
        'prefix' => 'Bearer ',
        'value' => env('BEARER_TOKEN')
    ],
    'basic_auth' => [
        'type' => 'basic',
        'username' => env('API_USERNAME'),
        'password' => env('API_PASSWORD')
    ]
];
```

## Usage Examples

### Basic HTTP Methods

```php
$guzzle = \Config\Services::guzzle();

// GET request with query parameters
$response = $guzzle->get('https://api.example.com/users', [
    'page' => 1,
    'limit' => 10
]);

// POST request with JSON data
$response = $guzzle->post('https://api.example.com/users', [
    'name' => 'John Doe',
    'email' => '<EMAIL>'
]);

// PUT request
$response = $guzzle->put('https://api.example.com/users/123', [
    'name' => 'Jane Doe'
]);

// DELETE request
$response = $guzzle->delete('https://api.example.com/users/123');
```

### Using Endpoint Presets

```php
// Get service configured for specific endpoint
$api = \Config\Services::guzzleEndpoint('my_api');

// Now all requests use the endpoint's base URL and settings
$response = $api->get('/users'); // Requests to https://api.myservice.com/v1/users
```

### Using Authentication

```php
// Using auth preset
$api = \Config\Services::guzzleWithAuth('bearer_token', [
    'value' => 'your-token-here'
]);

$response = $api->get('https://api.example.com/protected-resource');
```

### File Operations

```php
// Upload file
$response = $guzzle->postForm('https://api.example.com/upload', [
    'file' => [
        'file' => '/path/to/local/file.jpg',
        'filename' => 'uploaded-file.jpg'
    ],
    'description' => 'My uploaded file'
]);

// Download file
$success = $guzzle->downloadFile(
    'https://example.com/file.pdf',
    '/local/path/downloaded-file.pdf'
);
```

### Error Handling

```php
try {
    $response = $guzzle->get('https://api.example.com/data');
    
    if ($response['success']) {
        $data = $response['data'];
        // Process successful response
    } else {
        // Handle HTTP error (4xx, 5xx)
        $statusCode = $response['status_code'];
        log_message('error', "API request failed with status: {$statusCode}");
    }
    
} catch (\Exception $e) {
    // Handle connection errors, timeouts, etc.
    log_message('error', 'HTTP request failed: ' . $e->getMessage());
}
```

### Using in Your Modules

#### In a Controller

```php
<?php

namespace Modules\YourModule\Controllers;

use CodeIgniter\Controller;

class ApiController extends Controller
{
    protected $guzzle;
    
    public function __construct()
    {
        $this->guzzle = \Config\Services::guzzle();
        helper('Modules\GuzzleHttp\Helpers\guzzle');
    }
    
    public function fetchExternalData()
    {
        try {
            $response = $this->guzzle->get('https://api.example.com/data');
            
            if (http_is_success($response)) {
                $data = http_get_data($response);
                return $this->response->setJSON($data);
            }
            
            return $this->response->setStatusCode(500)
                                 ->setJSON(['error' => 'External API failed']);
                                 
        } catch (\Exception $e) {
            log_message('error', 'API request failed: ' . $e->getMessage());
            return $this->response->setStatusCode(500)
                                 ->setJSON(['error' => 'Request failed']);
        }
    }
}
```

#### In a Model

```php
<?php

namespace Modules\YourModule\Models;

use CodeIgniter\Model;

class ExternalApiModel extends Model
{
    protected $guzzle;
    
    public function __construct()
    {
        parent::__construct();
        $this->guzzle = \Config\Services::guzzleEndpoint('my_api');
    }
    
    public function syncUserData($userId)
    {
        $response = $this->guzzle->get("/users/{$userId}");
        
        if ($response['success']) {
            return $response['data'];
        }
        
        throw new \Exception('Failed to fetch user data from external API');
    }
}
```

## Helper Functions Reference

| Function | Description |
|----------|-------------|
| `http_get($url, $query, $headers, $options)` | Perform GET request |
| `http_post($url, $data, $headers, $options)` | Perform POST request |
| `http_put($url, $data, $headers, $options)` | Perform PUT request |
| `http_patch($url, $data, $headers, $options)` | Perform PATCH request |
| `http_delete($url, $headers, $options)` | Perform DELETE request |
| `http_download($url, $savePath, $headers, $options)` | Download file |
| `http_post_form($url, $formData, $headers, $options)` | Send form data |
| `http_with_auth($preset, $credentials)` | Get authenticated client |
| `http_endpoint($name, $options)` | Get endpoint-configured client |
| `http_is_success($response)` | Check if response is successful |
| `http_get_status($response)` | Get HTTP status code |
| `http_get_data($response)` | Get response data |
| `http_get_headers($response)` | Get response headers |

## Response Format

All methods return a consistent response format:

```php
[
    'success' => true,           // Boolean indicating success (2xx status)
    'status_code' => 200,        // HTTP status code
    'headers' => [...],          // Response headers array
    'data' => [...],             // Parsed response data (JSON decoded if applicable)
    'raw_body' => '...'          // Raw response body string
]
```

## Logging

The module automatically logs:
- Successful requests (INFO level)
- Failed requests (ERROR level)
- Connection errors (ERROR level)

Logs include method, URL, status code, and error details.

## Best Practices

1. **Use endpoint presets** for frequently accessed APIs
2. **Handle errors gracefully** with try-catch blocks
3. **Use helper functions** for simple one-off requests
4. **Configure timeouts** appropriately for your use case
5. **Use authentication presets** for secure APIs
6. **Log important requests** for debugging and monitoring

## Troubleshooting

### SSL Certificate Issues
```php
// In development, you can disable SSL verification
$guzzle = \Config\Services::guzzle(['verify' => false]);
```

### Timeout Issues
```php
// Increase timeout for slow APIs
$guzzle = \Config\Services::guzzle(['timeout' => 60]);
```

### Debug Requests
Enable response logging in configuration:
```php
public $logging = [
    'log_responses' => true  // Enable in development only
];
```
