<?php

namespace Modules\FameManagement\Controllers;

use App\Controllers\AdminController;
use CodeIgniter\HTTP\ResponseInterface;

use \Modules\FameManagement\Models\VpsModel;
use \Modules\FameManagement\Models\EpsModel;
use \Modules\CitemDbManagement\Models\CitemVpsModel;
// use \Modules\FameManagement\Models\GuzzleModel;

use \Hermawan\DataTables\DataTable;

class FameVpsController extends AdminController
{

    protected $vps_model;
    protected $eps_model;
    protected $guzzleModel;

    public function __construct()
    {
        parent::__construct();

        $this->vps_model = new VpsModel();
        $this->eps_model = new EpsModel();
        // $this->guzzleModel = new GuzzleModel();
    }


    public function index()
    {
        $filter = [
            'a.status' => 1,
            'account_type' => 1, //trade buyer
            'a.mf_event' => 'yes', //joining the event for the year
        ];  
        $fameplus = $this->vps_model->getUsers($filter);
        //get count
        $fameplus_count = count($fameplus);
        //get fplus_email from $fameplus
        $emails = array_column($fameplus, 'fplus_email');

        $filter2 = [
            'b.validation_status' => 'Approved Trade', //visitor
            'b.visitor_type' => 'Trade Buyer', //joining the event for the year
            'b.sector' => '02',
        ];  
        $whereFilter = [
            // 'b.fair_code' => 'MFIO',
            // 'b.fair_code' => 'MFIA',
        ];

        $inFilter = [
            'a.email' => $emails,
        ];

        $v_citem_model = new CitemVpsModel();
        $citem_vps = $v_citem_model->getVpsList($filter2,$whereFilter,$inFilter);

        foreach ($fameplus as $contact) {
            $found = false;
            foreach ($citem_vps as $vps) {
                if ($contact['fplus_email'] == $vps['citem_email']) {
                    $combinedData[] = array_merge($vps, $contact);
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $contact['citemdb_status'] = '';
                $combinedData[] = $contact;
            }
        }

        // dd($combinedData);

        $data = [
            'title' => 'FamePlus VPS',
            'page_title' => 'FamePlus VPS List',
            'data' => $combinedData,
            'fameplus_count' => $fameplus_count,
        ];

        return view('Modules\FameManagement\Views\index', $data);
    }

    public function apiData(){
        $category = $this->vps_model->getCategory();
        data($category);
        $data = $this->vps_model->getApiData();
        dd($data);
        if ($this->request->isAJAX()) {
            $data = $this->vps_model->getApiData();
            dd($data);
            return $this->response->setJSON($data);
        }
        return $this->response->setStatusCode(403);
    }

}
