<?php

/**
 * Navigation Menu Module Routes
 */

$routes->group('admin/navigation', ['namespace' => 'Modules\NavigationMenu\Controllers'], function ($routes) {
    $routes->get('/', 'NavigationController::index');
    $routes->get('create', 'NavigationController::create');
    $routes->post('store', 'NavigationController::store');
    $routes->get('edit/(:num)', 'NavigationController::edit/$1');
    $routes->post('update/(:num)', 'NavigationController::update/$1');
    $routes->post('delete/(:num)', 'NavigationController::delete/$1');
    $routes->post('reorder', 'NavigationController::reorder');
});
