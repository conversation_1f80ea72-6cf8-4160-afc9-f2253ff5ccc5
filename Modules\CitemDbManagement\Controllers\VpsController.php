<?php

namespace Modules\CitemDbManagement\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

use Modules\CitemDbManagement\Models\CitemVpsModel;
use Modules\CitemDbManagement\Models\CitemEpsModel;
use Modules\CitemDbManagement\Config\CitemDbManagement;

use \Hermawan\DataTables\DataTable;

class VpsController extends BaseController
{
    protected $citem_vps_model;
    protected $citem_eps_model;

    public function __construct()
    {
        $this->citem_vps_model = new CitemVpsModel();
        $this->citem_eps_model = new CitemEpsModel();
    }

    public function index()
    {
        echo 1;exit();
        $title = 'VPS';
        $page_title = 'VPS List';
        $page_subtitle = 'List of VPS';
        $breadcrumb = [
            ['label' => 'Dashboard', 'url' => route_to('admin.dashboard')],
            ['label' => 'VPS', 'url' => route_to('citemdb.vps.index')],
        ];
        $data = [
            'title' => $title,
            'page_title' => $page_title,
            'page_subtitle' => $page_subtitle,
            'breadcrumb' => $breadcrumb,
            'config' => config('CitemDbManagement'),
        ];

        dd($data);

        $test = $this->citem_vps_model->getVpsList();

        return view('Modules\CitemDbManagement\Views\vps\index', $data);
    }

    public function datatable(array $filter = []){
        $filter = [
            'b.validation_status' => 'Approved Trade',
        ];
        $test = $this->citem_vps_model->getVpsList($filter);
        dd($test);


        if ($this->request->isAJAX()) {
            $data = $this->citem_vps_model->getVpsList($filter);
            return $this->response->setJSON($data);
        }
    }

}
