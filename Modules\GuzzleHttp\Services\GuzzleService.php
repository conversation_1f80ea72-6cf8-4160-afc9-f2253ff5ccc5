<?php

namespace Modules\GuzzleHttp\Services;

use Guz<PERSON><PERSON>ttp\Client;
use Guz<PERSON>Http\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Guz<PERSON>Http\Exception\ClientException;
use Guz<PERSON>Http\Exception\ServerException;
use GuzzleHttp\Psr7\Response;
use CodeIgniter\Log\Logger;
use Config\Services;

/**
 * Guzzle HTTP Service
 * 
 * A comprehensive wrapper for GuzzleHttp\Client that provides:
 * - Easy-to-use methods for common HTTP operations
 * - Consistent error handling and logging
 * - Response formatting and validation
 * - Configurable timeouts and retry logic
 * - Request/response logging for debugging
 */
class GuzzleService
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var array Default configuration options
     */
    protected $defaultOptions = [
        'timeout' => 30,
        'connect_timeout' => 10,
        'verify' => true,
        'http_errors' => false, // We'll handle errors manually
        'headers' => [
            'User-Agent' => 'CodeIgniter4-GuzzleModule/1.0',
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ]
    ];

    /**
     * Constructor
     * 
     * @param array $options Additional Guzzle client options
     */
    public function __construct(array $options = [])
    {
        $this->logger = Services::logger();
        
        // Merge custom options with defaults
        $clientOptions = array_merge($this->defaultOptions, $options);
        
        $this->client = new Client($clientOptions);
    }

    /**
     * Perform a GET request
     * 
     * @param string $url
     * @param array $query Query parameters
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    public function get(string $url, array $query = [], array $headers = [], array $options = []): array
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);
        
        if (!empty($query)) {
            $requestOptions['query'] = $query;
        }

        return $this->makeRequest('GET', $url, $requestOptions);
    }

    /**
     * Perform a POST request
     * 
     * @param string $url
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    public function post(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);
        
        if (!empty($data)) {
            $requestOptions['json'] = $data;
        }

        return $this->makeRequest('POST', $url, $requestOptions);
    }

    /**
     * Perform a PUT request
     * 
     * @param string $url
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    public function put(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);
        
        if (!empty($data)) {
            $requestOptions['json'] = $data;
        }

        return $this->makeRequest('PUT', $url, $requestOptions);
    }

    /**
     * Perform a PATCH request
     * 
     * @param string $url
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    public function patch(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);
        
        if (!empty($data)) {
            $requestOptions['json'] = $data;
        }

        return $this->makeRequest('PATCH', $url, $requestOptions);
    }

    /**
     * Perform a DELETE request
     * 
     * @param string $url
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    public function delete(string $url, array $headers = [], array $options = []): array
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);

        return $this->makeRequest('DELETE', $url, $requestOptions);
    }

    /**
     * Send form data (multipart/form-data)
     * 
     * @param string $url
     * @param array $formData Form fields and files
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    public function postForm(string $url, array $formData = [], array $headers = [], array $options = []): array
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);
        
        // Remove Content-Type header for multipart requests (Guzzle will set it)
        if (isset($requestOptions['headers']['Content-Type'])) {
            unset($requestOptions['headers']['Content-Type']);
        }
        
        if (!empty($formData)) {
            $requestOptions['multipart'] = $this->buildMultipartData($formData);
        }

        return $this->makeRequest('POST', $url, $requestOptions);
    }

    /**
     * Download a file
     * 
     * @param string $url
     * @param string $savePath Local path to save the file
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return bool Success status
     * @throws \Exception
     */
    public function downloadFile(string $url, string $savePath, array $headers = [], array $options = []): bool
    {
        $requestOptions = $this->buildRequestOptions($options, $headers);
        $requestOptions['sink'] = $savePath;

        try {
            $response = $this->client->request('GET', $url, $requestOptions);
            
            $this->logRequest('GET', $url, $requestOptions, $response);
            
            return $response->getStatusCode() >= 200 && $response->getStatusCode() < 300;
            
        } catch (GuzzleException $e) {
            $this->logError('GET', $url, $requestOptions, $e);
            throw new \Exception('File download failed: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Make the actual HTTP request
     * 
     * @param string $method
     * @param string $url
     * @param array $options
     * @return array
     * @throws \Exception
     */
    protected function makeRequest(string $method, string $url, array $options): array
    {
        try {
            $response = $this->client->request($method, $url, $options);
            
            $this->logRequest($method, $url, $options, $response);
            
            return $this->formatResponse($response);
            
        } catch (GuzzleException $e) {
            $this->logError($method, $url, $options, $e);
            throw $this->handleException($e);
        }
    }

    /**
     * Build request options array
     * 
     * @param array $options
     * @param array $headers
     * @return array
     */
    protected function buildRequestOptions(array $options, array $headers): array
    {
        $requestOptions = $options;
        
        if (!empty($headers)) {
            $requestOptions['headers'] = array_merge(
                $this->defaultOptions['headers'],
                $headers
            );
        }
        
        return $requestOptions;
    }

    /**
     * Build multipart data for form submissions
     * 
     * @param array $formData
     * @return array
     */
    protected function buildMultipartData(array $formData): array
    {
        $multipart = [];
        
        foreach ($formData as $name => $value) {
            if (is_array($value) && isset($value['file'])) {
                // File upload
                $multipart[] = [
                    'name' => $name,
                    'contents' => fopen($value['file'], 'r'),
                    'filename' => $value['filename'] ?? basename($value['file'])
                ];
            } else {
                // Regular field
                $multipart[] = [
                    'name' => $name,
                    'contents' => $value
                ];
            }
        }
        
        return $multipart;
    }

    /**
     * Format the response into a consistent array structure
     * 
     * @param Response $response
     * @return array
     */
    protected function formatResponse(Response $response): array
    {
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        
        // Try to decode JSON response
        $data = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $data = $body;
        }
        
        return [
            'success' => $statusCode >= 200 && $statusCode < 300,
            'status_code' => $statusCode,
            'headers' => $response->getHeaders(),
            'data' => $data,
            'raw_body' => $body
        ];
    }

    /**
     * Handle Guzzle exceptions and convert to appropriate exceptions
     * 
     * @param GuzzleException $e
     * @return \Exception
     */
    protected function handleException(GuzzleException $e): \Exception
    {
        if ($e instanceof ConnectException) {
            return new \Exception('Connection failed: ' . $e->getMessage(), 0, $e);
        }
        
        if ($e instanceof ClientException) {
            $response = $e->getResponse();
            $statusCode = $response ? $response->getStatusCode() : 400;
            return new \Exception('Client error: ' . $e->getMessage(), $statusCode, $e);
        }
        
        if ($e instanceof ServerException) {
            $response = $e->getResponse();
            $statusCode = $response ? $response->getStatusCode() : 500;
            return new \Exception('Server error: ' . $e->getMessage(), $statusCode, $e);
        }
        
        return new \Exception('HTTP request failed: ' . $e->getMessage(), 0, $e);
    }

    /**
     * Log successful requests
     * 
     * @param string $method
     * @param string $url
     * @param array $options
     * @param Response $response
     */
    protected function logRequest(string $method, string $url, array $options, Response $response): void
    {
        $this->logger->info('HTTP Request', [
            'method' => $method,
            'url' => $url,
            'status_code' => $response->getStatusCode(),
            'response_size' => $response->getBody()->getSize()
        ]);
    }

    /**
     * Log request errors
     * 
     * @param string $method
     * @param string $url
     * @param array $options
     * @param GuzzleException $exception
     */
    protected function logError(string $method, string $url, array $options, GuzzleException $exception): void
    {
        $this->logger->error('HTTP Request Failed', [
            'method' => $method,
            'url' => $url,
            'error' => $exception->getMessage(),
            'code' => $exception->getCode()
        ]);
    }

    /**
     * Get the underlying Guzzle client for advanced usage
     * 
     * @return Client
     */
    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * Set custom timeout for requests
     * 
     * @param int $timeout Timeout in seconds
     * @return self
     */
    public function setTimeout(int $timeout): self
    {
        $this->defaultOptions['timeout'] = $timeout;
        return $this;
    }

    /**
     * Set custom headers for all requests
     * 
     * @param array $headers
     * @return self
     */
    public function setHeaders(array $headers): self
    {
        $this->defaultOptions['headers'] = array_merge($this->defaultOptions['headers'], $headers);
        return $this;
    }
}
