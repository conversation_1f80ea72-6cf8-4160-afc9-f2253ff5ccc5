<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row row-deck row-cards">
    <div class="col-12">
        <form action="<?= route_to('menu.store') ?>" method="post">
            <?= csrf_field() ?>
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Create New Menu</h3>
                </div>
                <div class="card-body">
                    <!-- Validation Errors -->
                    <?php if (session()->has('errors')): ?>
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div><i class="ti ti-alert-circle icon alert-icon"></i></div>
                            <div>
                                <h4 class="alert-title">Validation Error!</h4>
                                <ul class="mb-0">
                                    <?php foreach (session('errors') as $error): ?>
                                        <li><?= esc($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    <?php endif; ?>

                    <!-- Flash Messages -->
                    <?= tabler_flash_messages() ?>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="mb-3">
                                <label class="form-label required">Menu Label</label>
                                <input type="text" class="form-control <?= session('errors.label') ? 'is-invalid' : '' ?>"
                                       name="label" value="<?= old('label') ?>" placeholder="Enter menu label" required>
                                <?php if (session('errors.label')): ?>
                                <div class="invalid-feedback"><?= session('errors.label') ?></div>
                                <?php endif; ?>
                                <small class="form-hint">The display name for the menu item</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">URL</label>
                                <input type="text" class="form-control <?= session('errors.url') ? 'is-invalid' : '' ?>"
                                       name="url" value="<?= old('url') ?>" placeholder="Enter URL (optional)">
                                <small class="form-hint">Leave empty for parent menus or use relative URLs like /admin/users</small>
                                <?php if (session('errors.url')): ?>
                                <div class="invalid-feedback"><?= session('errors.url') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Icon</label>
                                <input type="text" class="form-control <?= session('errors.icon') ? 'is-invalid' : '' ?>"
                                       name="icon" value="<?= old('icon') ?>" placeholder="Enter icon class (optional)" id="icon">
                                <small class="form-hint">Use Tabler icons like: ti ti-home, ti ti-user, etc.</small>
                                <?php if (session('errors.icon')): ?>
                                <div class="invalid-feedback"><?= session('errors.icon') ?></div>
                                <?php endif; ?>
                                <div id="icon-preview" class="mt-2"></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">CSS Class</label>
                                <input type="text" class="form-control <?= session('errors.css_class') ? 'is-invalid' : '' ?>"
                                       name="css_class" value="<?= old('css_class') ?>" placeholder="Additional CSS classes (optional)">
                                <small class="form-hint">Additional CSS classes for custom styling</small>
                                <?php if (session('errors.css_class')): ?>
                                <div class="invalid-feedback"><?= session('errors.css_class') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-lg-4">

                            <div class="mb-3">
                                <label class="form-label">Parent Menu</label>
                                <select class="form-select <?= session('errors.parent_id') ? 'is-invalid' : '' ?>" name="parent_id">
                                    <option value="">Select Parent Menu (optional)</option>
                                    <?php if (!empty($parentMenus)): ?>
                                        <?php foreach ($parentMenus as $id => $label): ?>
                                            <option value="<?= $id ?>" <?= old('parent_id') == $id ? 'selected' : '' ?>>
                                                <?= esc($label) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <?php if (session('errors.parent_id')): ?>
                                <div class="invalid-feedback"><?= session('errors.parent_id') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Target</label>
                                <select class="form-select <?= session('errors.target') ? 'is-invalid' : '' ?>" name="target">
                                    <option value="_self" <?= old('target', '_self') == '_self' ? 'selected' : '' ?>>Same Window (_self)</option>
                                    <option value="_blank" <?= old('target') == '_blank' ? 'selected' : '' ?>>New Window (_blank)</option>
                                    <option value="_parent" <?= old('target') == '_parent' ? 'selected' : '' ?>>Parent Frame (_parent)</option>
                                    <option value="_top" <?= old('target') == '_top' ? 'selected' : '' ?>>Full Window (_top)</option>
                                </select>
                                <?php if (session('errors.target')): ?>
                                <div class="invalid-feedback"><?= session('errors.target') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Sort Order</label>
                                <input type="number" class="form-control <?= session('errors.sort_order') ? 'is-invalid' : '' ?>"
                                       name="sort_order" value="<?= old('sort_order', 0) ?>" min="0">
                                <small class="form-hint">Lower numbers appear first</small>
                                <?php if (session('errors.sort_order')): ?>
                                <div class="invalid-feedback"><?= session('errors.sort_order') ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Required Roles</label>
                                <select name="role_ids[]" id="role_ids" class="form-select" multiple data-placeholder="Select roles that can access this menu">
                                    <?php if (!empty($roles)): ?>
                                        <?php foreach ($roles as $role): ?>
                                            <option value="<?= $role['id'] ?>" <?= in_array($role['id'], old('role_ids', [])) ? 'selected' : '' ?>>
                                                <?= esc($role['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <small class="form-hint">Users with these roles can access this menu. Leave empty for public access.</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="active" value="1" <?= old('active', '1') ? 'checked' : '' ?>>
                                    <span class="form-check-label">Active</span>
                                </label>
                                <small class="form-hint">Inactive menus will not be displayed</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-end">
                    <div class="d-flex">
                        <a href="<?= route_to('menu.index') ?>" class="btn btn-link">Cancel</a>
                        <button type="submit" class="btn btn-primary ms-auto">Create Menu</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Tom Select for multi-select -->
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/js/tom-select.complete.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/css/tom-select.css" rel="stylesheet">

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Tom Select for role selection
    if (document.getElementById('role_ids')) {
        new TomSelect('#role_ids', {
            plugins: ['remove_button'],
            placeholder: 'Select roles that can access this menu',
            allowEmptyOption: true,
            create: false
        });
    }

    // Icon preview functionality
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');

    if (iconInput && iconPreview) {
        iconInput.addEventListener('input', function() {
            const iconClass = this.value.trim();
            if (iconClass) {
                // Use PHP helper to get SVG or fallback to CSS class
                fetch('<?= base_url('admin/menu/preview-icon') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                    },
                    body: JSON.stringify({ icon: iconClass })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        iconPreview.innerHTML = `<div class="d-flex align-items-center gap-2 text-muted">
                            ${data.icon_html}
                            <span>Preview</span>
                        </div>`;
                    } else {
                        // Fallback to CSS class
                        iconPreview.innerHTML = `<div class="d-flex align-items-center gap-2 text-muted">
                            <i class="${iconClass}"></i>
                            <span>Preview</span>
                        </div>`;
                    }
                })
                .catch(() => {
                    // Fallback to CSS class on error
                    iconPreview.innerHTML = `<div class="d-flex align-items-center gap-2 text-muted">
                        <i class="${iconClass}"></i>
                        <span>Preview</span>
                    </div>`;
                });
            } else {
                iconPreview.innerHTML = '';
            }
        });

        // Trigger initial preview
        iconInput.dispatchEvent(new Event('input'));
    }
});
</script>
<?= $this->endSection() ?>
