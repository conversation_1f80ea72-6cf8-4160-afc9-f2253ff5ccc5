<?php

namespace Modules\CitemDbManagement\Models;

use CodeIgniter\Model;

class CitemVpsModel extends Model
{
    protected $DBGroup          = 'vcitemdb';
    protected $table            = 'v_contact_profile';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];


    public function getVpsList(array $where = [], array $whereLike = [], array $whereIn = [])
    {
        // $config = config('CitemDbManagement');
        // $fair_code = $config->fair_code;
        // $sector = $config->sector;

        $builder = $this->db()->table('v_contact_profile a')
                ->select("a.rep_code as rep_code,a.co_name as co_name,a.email as citem_email,a.fameplus_id as fameplus_id,GROUP_CONCAT(b.fair_code) as fair_codes,b.sector,b.validation_status,b.visitor_status,b.pre_reg,b.item_code")
                ->join('v_attendance b', 'a.rep_code = b.rep_code', 'inner');
                // ->where('b.fair_code', $fair_code)
                // ->where('b.sector', $sector);


        if (!empty($where)) {
            $builder->where($where);
        }
        if (!empty($whereLike)) {
            $builder->like($whereLike);
        }
        
        if (!empty($whereIn)) {
            foreach ($whereIn as $column => $values) {
                if (is_array($values)) {
                    $builder->whereIn($column, $values);
                }
            }
        }
        $builder->groupBy('a.email');

        // echo $builder->getCompiledSelect();exit();

        return $builder->get()->getResultArray();
    }
}
