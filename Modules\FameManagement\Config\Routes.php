<?php

namespace Modules\FameManagement\Config;


$routes->group('admin',function($routes){
    $routes->group('fame',['namespace'=>'Modules\FameManagement\Controllers'],function($routes){
        $routes->GET('/', 'FameVpsController::index', ['as' => 'fame.index']);


        //API endpoints
        $routes->GET('api-data', 'FameVpsController::apiData', ['as' => 'fame.api-data']);
        $routes->POST('api-post', 'FameVpsController::apiPost', ['as' => 'fame.api-post']);
        $routes->POST('api-patch', 'FameVpsController::apiPatch', ['as' => 'fame.api-patch']);

    
    });
});
