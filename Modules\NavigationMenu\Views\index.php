<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<?= tabler_page_header('Navigation Menu Management', [
    'pretitle' => 'System',
    'actions' => hasPermission('menu.manage') ? tabler_button('Add New Menu', 'primary', [
        'icon' => 'ti ti-plus',
        'href' => base_url('admin/navigation/create'),
        'size' => 'sm'
    ]) : '',
    'breadcrumb' => [
        ['text' => 'Dashboard', 'url' => base_url('admin')],
        ['text' => 'Navigation Menu Management']
    ]
]) ?>

<!-- Page Body -->
<div class="page-body">
    <div class="container-xl">

        <!-- Flash Messages -->
        <?= tabler_flash_messages() ?>

        <!-- Statistics Cards -->
        <div class="row row-deck row-cards mb-4">
            <div class="col-sm-6 col-lg-3">
                <?= tabler_stats_card('Total Menus', $statistics['total'], [
                    'icon' => 'ti ti-menu-2',
                    'color' => 'blue'
                ]) ?>
            </div>
            <div class="col-sm-6 col-lg-3">
                <?= tabler_stats_card('Active Menus', $statistics['active'], [
                    'icon' => 'ti ti-check',
                    'color' => 'green'
                ]) ?>
            </div>
            <div class="col-sm-6 col-lg-3">
                <?= tabler_stats_card('Inactive Menus', $statistics['inactive'], [
                    'icon' => 'ti ti-x',
                    'color' => 'yellow'
                ]) ?>
            </div>
            <div class="col-sm-6 col-lg-3">
                <?= tabler_stats_card('Root Menus', $statistics['root_menus'], [
                    'icon' => 'ti ti-hierarchy',
                    'color' => 'purple'
                ]) ?>
            </div>
        </div>

        <!-- Navigation Menu Table Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">All Navigation Menus</h3>
            </div>
            <div class="card-body">
                <!-- Bulk Actions -->
                <?php if (hasPermission('menu.manage')) : ?>
                <?= tabler_bulk_actions([
                    ['text' => 'Activate Selected', 'class' => 'btn-success', 'id' => 'bulk-activate', 'icon' => 'ti ti-check'],
                    ['text' => 'Deactivate Selected', 'class' => 'btn-warning', 'id' => 'bulk-deactivate', 'icon' => 'ti ti-x'],
                    ['text' => 'Delete Selected', 'class' => 'btn-danger', 'id' => 'bulk-delete', 'icon' => 'ti ti-trash']
                ]) ?>
                <?php endif; ?>

                <!-- DataTable -->
                <div class="table-responsive">
                    <table id="navigationTable" class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th width="30">#</th>
                                <?php if (hasPermission('menu.manage')) : ?>
                                <th width="30">
                                    <input type="checkbox" id="select-all" class="form-check-input">
                                </th>
                                <?php endif; ?>
                                <th>Menu Title</th>
                                <th>URL</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Menu View Modal -->
<?= tabler_modal('menuViewModal', 'Menu Details', '<div id="menuViewContent"><!-- Content will be loaded here --></div>', [
    'size' => 'lg'
]) ?>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables JS from CDN -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<!-- SweetAlert2 from CDN -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // CSRF token
    var csrfName = '<?= csrf_token() ?>';
    var csrfHash = '<?= csrf_hash() ?>';

    // Initialize DataTable with Hermawan DataTables
    var navigationTable = $('#navigationTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        ajax: {
            url: '<?= site_url('admin/navigation/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [[<?= hasPermission('menu.manage') ? '2' : '1' ?>, 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('menu.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'title' }
            ,{ data: 'url' }
            ,{ data: 'status_badge', orderable: false, searchable: false }
            ,{ data: 'created_date' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        language: {
            processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
            emptyTable: 'No navigation menus available',
            zeroRecords: 'No matching navigation menus found'
        }
    });

    // Select all checkbox functionality
    $('#select-all').on('change', function() {
        $('.select-item').prop('checked', this.checked);
        updateBulkButtons();
    });

    // Individual checkbox functionality
    $(document).on('change', '.select-item', function() {
        updateBulkButtons();

        // Update select all checkbox
        var totalCheckboxes = $('.select-item').length;
        var checkedCheckboxes = $('.select-item:checked').length;
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        var selectedCount = $('.select-item:checked').length;
        $('#selected-count').text(selectedCount + ' selected');

        if (selectedCount > 0) {
            $('#bulk-activate, #bulk-deactivate, #bulk-delete').prop('disabled', false);
        } else {
            $('#bulk-activate, #bulk-deactivate, #bulk-delete').prop('disabled', true);
        }
    }

    // Bulk delete functionality
    $('#bulk-delete').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            Swal.fire('Warning!', 'Please select menus to delete.', 'warning');
            return;
        }

        Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to delete ' + selectedIds.length + ' menu(s). This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d63384',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete them!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= site_url('admin/navigation/bulk-delete') ?>',
                    type: 'POST',
                    data: {
                        ids: selectedIds,
                        [csrfName]: csrfHash
                    },
                    success: function(response) {
                        if (response.success) {
                            navigationTable.ajax.reload();
                            $('#select-all').prop('checked', false);
                            updateBulkButtons();
                            Swal.fire('Deleted!', response.message, 'success');
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'An error occurred while deleting menus.', 'error');
                    }
                });
            }
        });
    });

    // Bulk activate functionality
    $('#bulk-activate').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            Swal.fire('Warning!', 'Please select menus to activate.', 'warning');
            return;
        }

        $.ajax({
            url: '<?= site_url('admin/navigation/bulk-activate') ?>',
            type: 'POST',
            data: {
                ids: selectedIds,
                [csrfName]: csrfHash
            },
            success: function(response) {
                if (response.success) {
                    navigationTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                    Swal.fire('Success!', response.message, 'success');
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'An error occurred while activating menus.', 'error');
            }
        });
    });

    // Bulk deactivate functionality
    $('#bulk-deactivate').on('click', function() {
        var selectedIds = [];
        $('.select-item:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            Swal.fire('Warning!', 'Please select menus to deactivate.', 'warning');
            return;
        }

        $.ajax({
            url: '<?= site_url('admin/navigation/bulk-deactivate') ?>',
            type: 'POST',
            data: {
                ids: selectedIds,
                [csrfName]: csrfHash
            },
            success: function(response) {
                if (response.success) {
                    navigationTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                    Swal.fire('Success!', response.message, 'success');
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'An error occurred while deactivating menus.', 'error');
            }
        });
    });

    // Delete single menu
    window.deleteMenu = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: 'You won\'t be able to revert this!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d63384',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= site_url('admin/navigation/delete') ?>/' + id,
                    type: 'POST',
                    data: {
                        [csrfName]: csrfHash
                    },
                    success: function(response) {
                        if (response.success) {
                            navigationTable.ajax.reload();
                            Swal.fire('Deleted!', response.message, 'success');
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'An error occurred while deleting the menu.', 'error');
                    }
                });
            }
        });
    };

    // View menu details
    window.viewMenu = function(id) {
        $.ajax({
            url: '<?= site_url('admin/navigation/view') ?>/' + id,
            type: 'GET',
            success: function(response) {
                $('#menuViewContent').html(response);
                var modal = new bootstrap.Modal(document.getElementById('menuViewModal'));
                modal.show();
            },
            error: function() {
                Swal.fire('Error!', 'An error occurred while loading menu details.', 'error');
            }
        });
    };
});
</script>
<?= $this->endSection() ?>
