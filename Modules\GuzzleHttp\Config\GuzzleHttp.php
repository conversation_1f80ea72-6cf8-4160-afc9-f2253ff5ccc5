<?php

namespace Modules\GuzzleHttp\Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Guzzle HTTP Configuration
 * 
 * This configuration file contains default settings for the Guzzle HTTP module.
 * You can override these settings in your environment-specific configuration files.
 */
class GuzzleHttp extends BaseConfig
{
    /**
     * Default timeout for HTTP requests (in seconds)
     * 
     * @var int
     */
    public $timeout = 30;

    /**
     * Default connection timeout (in seconds)
     * 
     * @var int
     */
    public $connectTimeout = 10;

    /**
     * Whether to verify SSL certificates
     * Set to false for development environments with self-signed certificates
     * 
     * @var bool
     */
    public $verifySSL = true;

    /**
     * Default headers to send with all requests
     * 
     * @var array
     */
    public $defaultHeaders = [
        'User-Agent' => 'CodeIgniter4-GuzzleModule/1.0',
        'Accept' => 'application/json',
        'Content-Type' => 'application/json'
    ];

    /**
     * Maximum number of redirects to follow
     * 
     * @var int
     */
    public $maxRedirects = 5;

    /**
     * Whether to allow redirects
     * 
     * @var bool
     */
    public $allowRedirects = true;

    /**
     * Default retry configuration
     * 
     * @var array
     */
    public $retry = [
        'enabled' => false,
        'max_attempts' => 3,
        'delay' => 1000, // milliseconds
        'multiplier' => 2.0,
        'max_delay' => 10000, // milliseconds
        'retry_on_status' => [500, 502, 503, 504]
    ];

    /**
     * Logging configuration
     * 
     * @var array
     */
    public $logging = [
        'enabled' => true,
        'log_requests' => true,
        'log_responses' => false, // Set to true for debugging (can be verbose)
        'log_errors' => true,
        'log_level' => 'info'
    ];

    /**
     * Proxy configuration (if needed)
     * 
     * @var array
     */
    public $proxy = [
        'enabled' => false,
        'http' => null,  // 'tcp://proxy.example.com:8080'
        'https' => null, // 'tcp://proxy.example.com:8080'
        'no' => []       // Array of domains to bypass proxy
    ];

    /**
     * Rate limiting configuration
     * 
     * @var array
     */
    public $rateLimiting = [
        'enabled' => false,
        'requests_per_minute' => 60,
        'burst_limit' => 10
    ];

    /**
     * Cache configuration for responses
     * 
     * @var array
     */
    public $cache = [
        'enabled' => false,
        'ttl' => 300, // seconds
        'cache_get_requests' => true,
        'cache_post_requests' => false
    ];

    /**
     * Authentication presets
     * You can define common authentication configurations here
     * 
     * @var array
     */
    public $authPresets = [
        'api_key' => [
            'type' => 'header',
            'header_name' => 'X-API-Key',
            'value' => null // Set in environment variables
        ],
        'bearer_token' => [
            'type' => 'header',
            'header_name' => 'Authorization',
            'prefix' => 'Bearer ',
            'value' => null // Set in environment variables
        ],
        'basic_auth' => [
            'type' => 'basic',
            'username' => null,
            'password' => null
        ]
    ];

    /**
     * Common API endpoints
     * Define frequently used API endpoints here for easy access
     * 
     * @var array
     */
    public $endpoints = [
        // Example:
        // 'jsonplaceholder' => [
        //     'base_url' => 'https://jsonplaceholder.typicode.com',
        //     'timeout' => 15,
        //     'headers' => [
        //         'Accept' => 'application/json'
        //     ]
        // ]
    ];

    /**
     * Environment-specific overrides
     * 
     * @var array
     */
    public $environments = [
        'development' => [
            'verifySSL' => false,
            'logging' => [
                'log_responses' => true
            ]
        ],
        'testing' => [
            'timeout' => 5,
            'verifySSL' => false
        ],
        'production' => [
            'verifySSL' => true,
            'logging' => [
                'log_responses' => false
            ]
        ]
    ];

    /**
     * Get configuration for current environment
     * 
     * @return array
     */
    public function getEnvironmentConfig(): array
    {
        $environment = ENVIRONMENT;
        
        if (isset($this->environments[$environment])) {
            return $this->environments[$environment];
        }
        
        return [];
    }

    /**
     * Get merged configuration with environment overrides
     * 
     * @return array
     */
    public function getMergedConfig(): array
    {
        $config = [
            'timeout' => $this->timeout,
            'connect_timeout' => $this->connectTimeout,
            'verify' => $this->verifySSL,
            'allow_redirects' => [
                'max' => $this->maxRedirects,
                'strict' => true,
                'referer' => true,
                'protocols' => ['http', 'https'],
                'track_redirects' => false
            ],
            'headers' => $this->defaultHeaders
        ];

        // Apply proxy settings if enabled
        if ($this->proxy['enabled']) {
            $config['proxy'] = [
                'http' => $this->proxy['http'],
                'https' => $this->proxy['https'],
                'no' => $this->proxy['no']
            ];
        }

        // Merge environment-specific overrides
        $envConfig = $this->getEnvironmentConfig();
        if (!empty($envConfig)) {
            $config = array_merge_recursive($config, $envConfig);
        }

        return $config;
    }

    /**
     * Get authentication configuration by preset name
     * 
     * @param string $presetName
     * @return array|null
     */
    public function getAuthPreset(string $presetName): ?array
    {
        return $this->authPresets[$presetName] ?? null;
    }

    /**
     * Get endpoint configuration by name
     * 
     * @param string $endpointName
     * @return array|null
     */
    public function getEndpoint(string $endpointName): ?array
    {
        return $this->endpoints[$endpointName] ?? null;
    }
}
