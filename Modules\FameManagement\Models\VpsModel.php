<?php

namespace Modules\FameManagement\Models;

use CodeIgniter\Model;

class VpsModel extends Model
{
    protected $DBGroup          = 'fplusDB';
    protected $table            = 'tbl_users';
    protected $primaryKey       = 'uid';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function getUsers(array $where = [])
    {
        return $this->db()->table('tbl_buyer a')
                ->select("a.id as fplus_id,a.email as fplus_email,a.company as fplus_company,
                CASE
            WHEN a.account_type = 1 THEN 'TRADE BUYER'
            WHEN a.account_type = 2 THEN 'VISITOR'
            ELSE 'UNKNOWN'
        END AS fplus_account_type,
        
        CASE
            WHEN a.status = 0 THEN 'incomplete'
            WHEN a.status = 1 THEN 'approved'
            WHEN a.status = 2 THEN 'pending'
            WHEN a.status = 3 THEN 'reviewed'
            WHEN a.status = 4 THEN 'deactivated'
            WHEN a.status = 5 THEN 'disapproved'
            ELSE 'unknown'
        END AS fplus_validation_status,
        
        CASE
            WHEN a.status = 0 THEN 'INCOMPLETE TRADE'
            WHEN a.status = 1 THEN 'APPROVED TRADE'
            WHEN a.status = 2 THEN 'PENDING TRADE'
            WHEN a.status = 3 THEN 'REVIEWED TRADE'
            WHEN a.status = 4 THEN 'DEACTIVATED TRADE'
            WHEN a.status = 5 THEN 'DISAPPROVED TRADE'
            ELSE 'UNKNOWN'
        END AS fplus_status
                ")
                ->where($where)
                ->get()->getResultArray();
    }

    public function getApiData(int $id=null){
        $this->db()->table('tbl_buyer a');
        if($id){
            $this->where('a.id', $id);
        }
        return $this->get()->getResultArray();
    }

    public function getCategory(){
        return $this->db()->table('tbl_product_category')->get()->getResultArray();
    }

    public function getSubCategory(){
        return $this->db()->table('tbl_product_sub_category')->get()->getResultArray();
    }
}



