<?php

/**
 * Guzzle HTTP Module Usage Examples
 * 
 * This file contains practical examples of how to use the Guzzle module
 * in your CodeIgniter 4 application across different modules.
 */

namespace Modules\GuzzleHttp\Examples;

use CodeIgniter\Controller;

class UsageExamples extends Controller
{
    /**
     * Example 1: Basic API consumption
     */
    public function basicApiExample()
    {
        // Load the Guzzle service
        $guzzle = \Config\Services::guzzle();
        
        try {
            // Fetch posts from JSONPlaceholder API
            $response = $guzzle->get('https://jsonplaceholder.typicode.com/posts', [
                'userId' => 1  // Query parameter
            ]);
            
            if ($response['success']) {
                $posts = $response['data'];
                echo "Found " . count($posts) . " posts\n";
                
                foreach ($posts as $post) {
                    echo "- {$post['title']}\n";
                }
            } else {
                echo "API request failed with status: " . $response['status_code'];
            }
            
        } catch (\Exception $e) {
            echo "Request failed: " . $e->getMessage();
        }
    }

    /**
     * Example 2: Using helper functions
     */
    public function helperFunctionsExample()
    {
        // Load the helper
        helper('Modules\GuzzleHttp\Helpers\guzzle');
        
        try {
            // Simple GET request
            $response = http_get('https://jsonplaceholder.typicode.com/users/1');
            
            if (http_is_success($response)) {
                $user = http_get_data($response);
                echo "User: {$user['name']} ({$user['email']})\n";
            }
            
            // Simple POST request
            $newPost = http_post('https://jsonplaceholder.typicode.com/posts', [
                'title' => 'My New Post',
                'body' => 'This is the content of my post',
                'userId' => 1
            ]);
            
            if (http_is_success($newPost)) {
                $post = http_get_data($newPost);
                echo "Created post with ID: {$post['id']}\n";
            }
            
        } catch (\Exception $e) {
            echo "Request failed: " . $e->getMessage();
        }
    }

    /**
     * Example 3: Authentication with API key
     */
    public function authenticationExample()
    {
        // Method 1: Using auth preset (configure in GuzzleHttp.php first)
        try {
            $api = \Config\Services::guzzleWithAuth('api_key', [
                'value' => 'your-api-key-here'
            ]);
            
            $response = $api->get('https://api.example.com/protected-data');
            
        } catch (\Exception $e) {
            echo "Authenticated request failed: " . $e->getMessage();
        }
        
        // Method 2: Manual headers
        $guzzle = \Config\Services::guzzle();
        
        try {
            $response = $guzzle->get('https://api.example.com/data', [], [
                'Authorization' => 'Bearer your-token-here',
                'X-API-Key' => 'your-api-key'
            ]);
            
        } catch (\Exception $e) {
            echo "Manual auth request failed: " . $e->getMessage();
        }
    }

    /**
     * Example 4: File upload
     */
    public function fileUploadExample()
    {
        $guzzle = \Config\Services::guzzle();
        
        try {
            // Upload a file with additional form data
            $response = $guzzle->postForm('https://httpbin.org/post', [
                'file' => [
                    'file' => WRITEPATH . 'uploads/example.txt',
                    'filename' => 'uploaded-example.txt'
                ],
                'description' => 'Test file upload',
                'category' => 'documents'
            ]);
            
            if ($response['success']) {
                echo "File uploaded successfully!\n";
                $data = $response['data'];
                echo "Server response: " . json_encode($data['files']);
            }
            
        } catch (\Exception $e) {
            echo "File upload failed: " . $e->getMessage();
        }
    }

    /**
     * Example 5: File download
     */
    public function fileDownloadExample()
    {
        $guzzle = \Config\Services::guzzle();
        
        try {
            $downloadPath = WRITEPATH . 'downloads/downloaded-file.jpg';
            
            // Ensure download directory exists
            if (!is_dir(dirname($downloadPath))) {
                mkdir(dirname($downloadPath), 0755, true);
            }
            
            $success = $guzzle->downloadFile(
                'https://via.placeholder.com/300x200.jpg',
                $downloadPath
            );
            
            if ($success) {
                echo "File downloaded to: {$downloadPath}\n";
                echo "File size: " . filesize($downloadPath) . " bytes\n";
            } else {
                echo "File download failed\n";
            }
            
        } catch (\Exception $e) {
            echo "Download failed: " . $e->getMessage();
        }
    }

    /**
     * Example 6: Using endpoint presets
     */
    public function endpointPresetExample()
    {
        // First, configure an endpoint in GuzzleHttp.php:
        /*
        public $endpoints = [
            'jsonplaceholder' => [
                'base_url' => 'https://jsonplaceholder.typicode.com',
                'timeout' => 15,
                'headers' => [
                    'Accept' => 'application/json'
                ]
            ]
        ];
        */
        
        try {
            // Get service configured for the endpoint
            $api = \Config\Services::guzzleEndpoint('jsonplaceholder');
            
            // Now all requests use the endpoint's base URL
            $users = $api->get('/users');
            $posts = $api->get('/posts');
            
            if ($users['success'] && $posts['success']) {
                echo "Users count: " . count($users['data']) . "\n";
                echo "Posts count: " . count($posts['data']) . "\n";
            }
            
        } catch (\Exception $e) {
            echo "Endpoint request failed: " . $e->getMessage();
        }
    }

    /**
     * Example 7: Error handling and retry logic
     */
    public function errorHandlingExample()
    {
        $guzzle = \Config\Services::guzzle();
        $maxRetries = 3;
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            try {
                $response = $guzzle->get('https://httpstat.us/500'); // This will return 500 error
                
                if ($response['success']) {
                    echo "Request succeeded!\n";
                    break;
                } else {
                    $retryCount++;
                    echo "Request failed with status {$response['status_code']}, retry {$retryCount}/{$maxRetries}\n";
                    
                    if ($retryCount < $maxRetries) {
                        sleep(1); // Wait before retry
                    }
                }
                
            } catch (\Exception $e) {
                $retryCount++;
                echo "Request exception: {$e->getMessage()}, retry {$retryCount}/{$maxRetries}\n";
                
                if ($retryCount < $maxRetries) {
                    sleep(1); // Wait before retry
                }
            }
        }
        
        if ($retryCount >= $maxRetries) {
            echo "All retry attempts failed\n";
        }
    }

    /**
     * Example 8: Working with different content types
     */
    public function contentTypesExample()
    {
        $guzzle = \Config\Services::guzzle();
        
        try {
            // JSON API request (default)
            $jsonResponse = $guzzle->post('https://httpbin.org/post', [
                'name' => 'John Doe',
                'email' => '<EMAIL>'
            ]);
            
            // XML API request
            $xmlResponse = $guzzle->post('https://httpbin.org/post', [], [
                'Content-Type' => 'application/xml'
            ], [
                'body' => '<user><name>John Doe</name><email><EMAIL></email></user>'
            ]);
            
            // Plain text request
            $textResponse = $guzzle->post('https://httpbin.org/post', [], [
                'Content-Type' => 'text/plain'
            ], [
                'body' => 'This is plain text data'
            ]);
            
            echo "JSON response status: " . $jsonResponse['status_code'] . "\n";
            echo "XML response status: " . $xmlResponse['status_code'] . "\n";
            echo "Text response status: " . $textResponse['status_code'] . "\n";
            
        } catch (\Exception $e) {
            echo "Content type request failed: " . $e->getMessage();
        }
    }

    /**
     * Example 9: Using in a model for data synchronization
     */
    public function modelIntegrationExample()
    {
        // This would typically be in your model file
        $guzzle = \Config\Services::guzzle();
        
        try {
            // Fetch external user data
            $response = $guzzle->get('https://jsonplaceholder.typicode.com/users');
            
            if ($response['success']) {
                $externalUsers = $response['data'];
                
                // Process and save to local database
                $db = \Config\Database::connect();
                $builder = $db->table('external_users');
                
                foreach ($externalUsers as $user) {
                    $userData = [
                        'external_id' => $user['id'],
                        'name' => $user['name'],
                        'email' => $user['email'],
                        'phone' => $user['phone'],
                        'website' => $user['website'],
                        'synced_at' => date('Y-m-d H:i:s')
                    ];
                    
                    // Insert or update
                    $existing = $builder->where('external_id', $user['id'])->get()->getRow();
                    
                    if ($existing) {
                        $builder->where('external_id', $user['id'])->update($userData);
                    } else {
                        $builder->insert($userData);
                    }
                }
                
                echo "Synchronized " . count($externalUsers) . " users\n";
            }
            
        } catch (\Exception $e) {
            echo "Data synchronization failed: " . $e->getMessage();
        }
    }

    /**
     * Example 10: Webhook handling
     */
    public function webhookExample()
    {
        // Send webhook notification
        $guzzle = \Config\Services::guzzle();
        
        $webhookData = [
            'event' => 'user.created',
            'data' => [
                'user_id' => 123,
                'name' => 'John Doe',
                'email' => '<EMAIL>'
            ],
            'timestamp' => time()
        ];
        
        try {
            $response = $guzzle->post('https://webhook.site/your-webhook-url', $webhookData, [
                'X-Webhook-Signature' => hash_hmac('sha256', json_encode($webhookData), 'your-secret-key')
            ]);
            
            if ($response['success']) {
                echo "Webhook sent successfully\n";
            } else {
                echo "Webhook failed with status: " . $response['status_code'] . "\n";
            }
            
        } catch (\Exception $e) {
            echo "Webhook sending failed: " . $e->getMessage();
        }
    }
}
