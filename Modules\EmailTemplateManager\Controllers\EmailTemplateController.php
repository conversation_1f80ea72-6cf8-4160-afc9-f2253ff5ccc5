<?php

namespace Modules\EmailTemplateManager\Controllers;

use App\Controllers\BaseController;
use Modules\EmailTemplateManager\Models\EmailTemplateModel;
use Modules\EmailTemplateManager\Models\EmailTemplateCategoryModel;
use Modules\EmailTemplateManager\Libraries\TemplateStorageService;
use Modules\EmailTemplateManager\Libraries\TemplateEngine;
use Modules\EmailTemplateManager\Config\EmailTemplateManager as EmailTemplateManagerConfig;
use CodeIgniter\HTTP\ResponseInterface;

class EmailTemplateController extends BaseController
{
    protected $templateModel;
    protected $categoryModel;
    protected $storageService;
    protected $templateEngine;
    
    public function __construct()
    {
        $this->templateModel = new EmailTemplateModel();
        $this->categoryModel = new EmailTemplateCategoryModel();
        $this->storageService = new TemplateStorageService();
        $this->templateEngine = new TemplateEngine();
    }
    
    public function index()
    {
        $data = [
            'title' => 'Email Template Manager',
            'page_title' => 'Email Templates',
            'categories' => $this->categoryModel->getActiveCategories(),
            // 'config' => new EmailTemplateManagerConfig()
        ];

        return view('Modules\EmailTemplateManager\Views\index', $data);
    }
    
    /**
     * Show create template form
     */
    public function create()
    {
        $data = [
            'title' => 'Create Email Template',
            'page_title' => 'Create New Template',
            'categories' => $this->categoryModel->getActiveCategories(),
            'config' => new EmailTemplateManagerConfig()
        ];
        
        return view('Modules\EmailTemplateManager\Views\create', $data);
    }
    
    /**
     * Store new template
     */
    public function store()
    {
        try {
            $rules = [
                'name' => 'required|max_length[255]',
                'slug' => 'permit_empty|max_length[255]|is_unique[email_templates.slug]',
                'subject' => 'required|max_length[500]',
                'category_id' => 'permit_empty|is_natural_no_zero',
                'html_content' => 'required',
                'design_json' => 'permit_empty'
            ];
            
            if (!$this->validate($rules)) {
                return $this->response->setJSON([
                    'success' => false,
                    'errors' => $this->validator->getErrors()
                ]);
            }
            
            $data = $this->request->getPost();
            
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = url_title($data['name'], '-', true);
            }
            
            // Save template file
            $fileName = $this->storageService->saveTemplate(
                $data['slug'],
                $data['html_content'],
                $data['design_json'] ?? null
            );
            
            // Extract placeholders from HTML content
            $placeholders = $this->extractPlaceholders($data['html_content']);
            
            // Save to database
            $templateData = [
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'] ?? null,
                'category_id' => $data['category_id'] ?? null,
                'subject' => $data['subject'],
                'file_path' => $fileName,
                'placeholders' => $placeholders,
                'is_active' => 1
            ];
            
            $templateId = $this->templateModel->insert($templateData);
            
            if (!$templateId) {
                // Clean up file if database insert failed
                $this->storageService->deleteTemplate($fileName);
                
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save template to database'
                ]);
            }
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Template created successfully',
                'template_id' => $templateId
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Template creation failed: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while creating the template'
            ]);
        }
    }
    
    /**
     * Show edit template form
     */
    public function edit($id)
    {
        $template = $this->templateModel->find($id);
        
        if (!$template) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Template not found');
        }
        
        // Load template content
        try {
            $templateData = $this->storageService->loadTemplate($template['file_path']);
            $template['html_content'] = $templateData['html'];
            $template['design_json'] = $templateData['design_json'] ?? null;
        } catch (\Exception $e) {
            $template['html_content'] = '';
            $template['design_json'] = null;
        }
        
        $data = [
            'title' => 'Edit Email Template',
            'page_title' => 'Edit Template: ' . $template['name'],
            'template' => $template,
            'categories' => $this->categoryModel->getActiveCategories(),
            'config' => new EmailTemplateManagerConfig()
        ];
        
        return view('Modules\EmailTemplateManager\Views\edit', $data);
    }
    
    /**
     * Update template
     */
    public function update($id)
    {
        try {
            $template = $this->templateModel->find($id);
            
            if (!$template) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Template not found'
                ]);
            }
            
            $rules = [
                'name' => 'required|max_length[255]',
                'slug' => "permit_empty|max_length[255]|is_unique[email_templates.slug,id,{$id}]",
                'subject' => 'required|max_length[500]',
                'category_id' => 'permit_empty|is_natural_no_zero',
                'html_content' => 'required',
                'design_json' => 'permit_empty'
            ];
            
            if (!$this->validate($rules)) {
                return $this->response->setJSON([
                    'success' => false,
                    'errors' => $this->validator->getErrors()
                ]);
            }
            
            $data = $this->request->getPost();
            
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = url_title($data['name'], '-', true);
            }
            
            // Update template file
            $fileName = $this->storageService->saveTemplate(
                $data['slug'],
                $data['html_content'],
                $data['design_json'] ?? null
            );
            
            // Delete old file
            if ($template['file_path'] !== $fileName) {
                $this->storageService->deleteTemplate($template['file_path']);
            }
            
            // Extract placeholders from HTML content
            $placeholders = $this->extractPlaceholders($data['html_content']);
            
            // Update database
            $updateData = [
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'] ?? null,
                'category_id' => $data['category_id'] ?? null,
                'subject' => $data['subject'],
                'file_path' => $fileName,
                'placeholders' => $placeholders
            ];
            
            $this->templateModel->update($id, $updateData);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Template updated successfully'
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Template update failed: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while updating the template'
            ]);
        }
    }
    
    /**
     * Delete template
     */
    public function delete($id)
    {
        try {
            $template = $this->templateModel->find($id);
            
            if (!$template) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Template not found'
                ]);
            }
            
            // Check if it's a system template
            if ($template['is_system']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'System templates cannot be deleted'
                ]);
            }
            
            // Delete from database (soft delete)
            $this->templateModel->delete($id);
            
            // Delete template file
            $this->storageService->deleteTemplate($template['file_path']);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Template deleted successfully'
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Template deletion failed: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while deleting the template'
            ]);
        }
    }
    
    /**
     * Get templates for AJAX requests
     */
    public function getTemplates()
    {
        $filters = [
            'category_id' => $this->request->getGet('category_id') ? (int)$this->request->getGet('category_id') : null,
            'is_active' => $this->request->getGet('is_active') ? (int)$this->request->getGet('is_active') : 1,
            'search' => $this->request->getGet('search'),
            'limit' => (int)($this->request->getGet('limit') ?? 10),
            'offset' => (int)($this->request->getGet('offset') ?? 0)
        ];

        $templates = $this->templateModel->getTemplatesWithCategory($filters);
        $total = $this->templateModel->where('is_active', $filters['is_active'])->countAllResults();

        return $this->response->setJSON([
            'success' => true,
            'data' => $templates,
            'total' => $total
        ]);
    }

    /**
     * DataTable endpoint for Hermawan DataTables
     */
    public function datatable()
    {
        $request = $this->request->getPost();

        // Get DataTable parameters
        $draw = (int)($request['draw'] ?? 1);
        $start = (int)($request['start'] ?? 0);
        $length = (int)($request['length'] ?? 10);
        $searchValue = $request['search']['value'] ?? '';

        // Custom filters
        $categoryId = $request['category_id'] ?? null;
        $status = $request['status'] ?? null;

        // Build query
        $builder = $this->templateModel->select('email_templates.*, email_template_categories.name as category_name, email_template_categories.color as category_color')
                                      ->join('email_template_categories', 'email_template_categories.id = email_templates.category_id', 'left');

        // Apply filters
        if ($categoryId) {
            $builder->where('email_templates.category_id', (int)$categoryId);
        }

        if ($status !== null && $status !== '') {
            $builder->where('email_templates.is_active', (int)$status);
        }

        // Apply search
        if ($searchValue) {
            $builder->groupStart()
                   ->like('email_templates.name', $searchValue)
                   ->orLike('email_templates.description', $searchValue)
                   ->orLike('email_templates.subject', $searchValue)
                   ->groupEnd();
        }

        // Get total count (before any filters)
        $totalBuilder = $this->templateModel->select('email_templates.*');
        $totalRecords = $totalBuilder->countAllResults(false);

        // Get filtered count (clone the builder to avoid affecting the main query)
        $countBuilder = clone $builder;
        $filteredRecords = $countBuilder->countAllResults(false);

        // Apply pagination and ordering
        $builder->orderBy('email_templates.updated_at', 'DESC')
               ->limit($length, $start);

        $templates = $builder->get()->getResultArray();

        // Format data for DataTable
        $data = [];
        foreach ($templates as $index => $template) {
            $data[] = [
                'DT_RowIndex' => $start + $index + 1,
                'id' => $template['id'],
                'name' => $template['name'],
                'description' => $template['description'],
                'category_name' => $template['category_name'],
                'category_color' => $template['category_color'],
                'subject' => $template['subject'],
                'is_active' => $template['is_active'],
                'usage_count' => $template['usage_count'] ?? 0,
                'updated_at' => date('M j, Y g:i A', strtotime($template['updated_at'])),
                'actions' => '' // Will be rendered by DataTable
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Preview template
     */
    public function preview($id)
    {
        try {
            $template = $this->templateModel->find($id);

            if (!$template) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Template not found');
            }

            // Load template content from file
            $templateData = $this->storageService->loadTemplate($template['file_path']);
            $htmlContent = $templateData['html'] ?? '<p>No content available</p>';

            // Get sample placeholders and replace them
            $samplePlaceholders = $this->getSamplePlaceholders($template['slug']);

            // Replace placeholders in HTML content
            foreach ($samplePlaceholders as $key => $value) {
                $htmlContent = str_replace('{{' . $key . '}}', $value, $htmlContent);
            }

            // Replace placeholders in subject
            $subject = $template['subject'];
            foreach ($samplePlaceholders as $key => $value) {
                $subject = str_replace('{{' . $key . '}}', $value, $subject);
            }

            $data = [
                'template' => $template,
                'html_content' => $htmlContent,
                'subject' => $subject
            ];

            return view('Modules\EmailTemplateManager\Views\preview', $data);

        } catch (\Exception $e) {
            log_message('error', 'Template preview failed: ' . $e->getMessage());
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Template preview not available: ' . $e->getMessage());
        }
    }

    /**
     * Send test email
     */
    public function sendTest()
    {
        try {
            $rules = [
                'template_id' => 'required|is_natural_no_zero',
                'email' => 'required|valid_email',
                'name' => 'permit_empty|max_length[255]'
            ];

            if (!$this->validate($rules)) {
                return $this->response->setJSON([
                    'success' => false,
                    'errors' => $this->validator->getErrors()
                ]);
            }

            $templateId = $this->request->getPost('template_id');
            $email = $this->request->getPost('email');
            $name = $this->request->getPost('name');

            $template = $this->templateModel->find($templateId);

            if (!$template) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Template not found'
                ]);
            }

            // Get sample placeholders
            $samplePlaceholders = $this->getSamplePlaceholders($template['slug']);
            $samplePlaceholders['user_name'] = $name ?: 'Test User';
            $samplePlaceholders['user_email'] = $email;

            // Send test email
            $result = $this->templateEngine->sendEmail($template['slug'], $email, $samplePlaceholders, $name);

            return $this->response->setJSON([
                'success' => $result,
                'message' => $result ? 'Test email sent successfully' : 'Failed to send test email'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Test email failed: ' . $e->getMessage());

            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while sending test email'
            ]);
        }
    }

    /**
     * Duplicate template
     */
    public function duplicate($id)
    {
        try {
            $template = $this->templateModel->find($id);

            if (!$template) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Template not found'
                ]);
            }

            // Create new slug
            $newSlug = $template['slug'] . '-copy-' . time();

            // Copy template file
            $newFileName = $this->storageService->copyTemplate($template['file_path'], $newSlug);

            // Create new database record
            $newTemplateData = $template;
            unset($newTemplateData['id'], $newTemplateData['created_at'], $newTemplateData['updated_at']);

            $newTemplateData['name'] = $template['name'] . ' (Copy)';
            $newTemplateData['slug'] = $newSlug;
            $newTemplateData['file_path'] = $newFileName;
            $newTemplateData['usage_count'] = 0;
            $newTemplateData['last_used_at'] = null;

            $newTemplateId = $this->templateModel->insert($newTemplateData);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Template duplicated successfully',
                'template_id' => $newTemplateId
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Template duplication failed: ' . $e->getMessage());

            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while duplicating the template'
            ]);
        }
    }

    /**
     * Test Unlayer editor (for debugging)
     */
    public function testUnlayer()
    {
        return view('Modules\EmailTemplateManager\Views\test-unlayer');
    }

    /**
     * Get sample placeholders for testing
     */
    protected function getSamplePlaceholders(string $templateSlug): array
    {
        $config = new EmailTemplateManagerConfig();
        $sampleData = [];

        // Add default placeholders
        foreach ($config->defaultPlaceholders as $key => $placeholder) {
            $sampleData[$key] = $placeholder['example'];
        }

        // Add template-specific placeholders
        if (isset($config->templatePlaceholders[$templateSlug])) {
            foreach ($config->templatePlaceholders[$templateSlug] as $key => $placeholder) {
                $sampleData[$key] = $placeholder['example'];
            }
        }

        return $sampleData;
    }

    /**
     * Extract placeholders from HTML content
     */
    protected function extractPlaceholders(string $htmlContent): array
    {
        $placeholders = [];

        // Find all {{placeholder}} and {placeholder} patterns
        preg_match_all('/\{\{?([^}]+)\}?\}/', $htmlContent, $matches);

        if (!empty($matches[1])) {
            foreach (array_unique($matches[1]) as $placeholder) {
                $placeholders[] = [
                    'key' => trim($placeholder),
                    'label' => ucwords(str_replace(['_', '-'], ' ', trim($placeholder))),
                    'required' => false,
                    'type' => 'text'
                ];
            }
        }

        return $placeholders;
    }
}
