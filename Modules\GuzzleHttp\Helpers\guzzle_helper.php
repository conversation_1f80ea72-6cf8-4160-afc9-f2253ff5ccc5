<?php

/**
 * Guzzle HTTP Helper Functions
 * 
 * These helper functions provide quick access to common HTTP operations
 * without needing to instantiate the service manually.
 * 
 * To use these helpers, load them in your controller or autoload them globally:
 * helper('Modules\GuzzleHttp\Helpers\guzzle');
 */

if (!function_exists('http_get')) {
    /**
     * Perform a quick GET request
     * 
     * @param string $url
     * @param array $query Query parameters
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    function http_get(string $url, array $query = [], array $headers = [], array $options = []): array
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->get($url, $query, $headers, $options);
    }
}

if (!function_exists('http_post')) {
    /**
     * Perform a quick POST request
     * 
     * @param string $url
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    function http_post(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->post($url, $data, $headers, $options);
    }
}

if (!function_exists('http_put')) {
    /**
     * Perform a quick PUT request
     * 
     * @param string $url
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    function http_put(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->put($url, $data, $headers, $options);
    }
}

if (!function_exists('http_patch')) {
    /**
     * Perform a quick PATCH request
     * 
     * @param string $url
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    function http_patch(string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->patch($url, $data, $headers, $options);
    }
}

if (!function_exists('http_delete')) {
    /**
     * Perform a quick DELETE request
     * 
     * @param string $url
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    function http_delete(string $url, array $headers = [], array $options = []): array
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->delete($url, $headers, $options);
    }
}

if (!function_exists('http_download')) {
    /**
     * Download a file from URL
     * 
     * @param string $url
     * @param string $savePath Local path to save the file
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return bool Success status
     * @throws \Exception
     */
    function http_download(string $url, string $savePath, array $headers = [], array $options = []): bool
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->downloadFile($url, $savePath, $headers, $options);
    }
}

if (!function_exists('http_post_form')) {
    /**
     * Send form data (multipart/form-data)
     * 
     * @param string $url
     * @param array $formData Form fields and files
     * @param array $headers Additional headers
     * @param array $options Additional Guzzle options
     * @return array Response data
     * @throws \Exception
     */
    function http_post_form(string $url, array $formData = [], array $headers = [], array $options = []): array
    {
        $guzzle = \Config\Services::guzzle();
        return $guzzle->postForm($url, $formData, $headers, $options);
    }
}

if (!function_exists('http_with_auth')) {
    /**
     * Get a Guzzle service instance with authentication
     * 
     * @param string $authPreset Name of the auth preset from configuration
     * @param array $credentials Credentials to use
     * @return \Modules\GuzzleHttp\Services\GuzzleService
     * @throws \InvalidArgumentException
     */
    function http_with_auth(string $authPreset, array $credentials = []): \Modules\GuzzleHttp\Services\GuzzleService
    {
        return \Config\Services::guzzleWithAuth($authPreset, $credentials);
    }
}

if (!function_exists('http_endpoint')) {
    /**
     * Get a Guzzle service instance configured for a specific endpoint
     * 
     * @param string $endpointName Name of the endpoint from configuration
     * @param array $options Additional options
     * @return \Modules\GuzzleHttp\Services\GuzzleService
     * @throws \InvalidArgumentException
     */
    function http_endpoint(string $endpointName, array $options = []): \Modules\GuzzleHttp\Services\GuzzleService
    {
        return \Config\Services::guzzleEndpoint($endpointName, $options);
    }
}

if (!function_exists('http_quick')) {
    /**
     * Get a quick HTTP client for one-off requests
     * 
     * @param array $options Guzzle client options
     * @return \Modules\GuzzleHttp\Services\GuzzleService
     */
    function http_quick(array $options = []): \Modules\GuzzleHttp\Services\GuzzleService
    {
        return \Config\Services::quickHttp($options);
    }
}

if (!function_exists('http_json_api')) {
    /**
     * Helper for JSON API requests with common headers
     * 
     * @param string $method HTTP method
     * @param string $url
     * @param array $data Request data
     * @param array $headers Additional headers
     * @param array $options Additional options
     * @return array Response data
     * @throws \Exception
     */
    function http_json_api(string $method, string $url, array $data = [], array $headers = [], array $options = []): array
    {
        $defaultHeaders = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];
        
        $mergedHeaders = array_merge($defaultHeaders, $headers);
        $guzzle = \Config\Services::guzzle();
        
        switch (strtoupper($method)) {
            case 'GET':
                return $guzzle->get($url, $data, $mergedHeaders, $options);
            case 'POST':
                return $guzzle->post($url, $data, $mergedHeaders, $options);
            case 'PUT':
                return $guzzle->put($url, $data, $mergedHeaders, $options);
            case 'PATCH':
                return $guzzle->patch($url, $data, $mergedHeaders, $options);
            case 'DELETE':
                return $guzzle->delete($url, $mergedHeaders, $options);
            default:
                throw new \InvalidArgumentException("Unsupported HTTP method: {$method}");
        }
    }
}

if (!function_exists('http_is_success')) {
    /**
     * Check if HTTP response indicates success
     * 
     * @param array $response Response array from Guzzle service
     * @return bool
     */
    function http_is_success(array $response): bool
    {
        return isset($response['success']) && $response['success'] === true;
    }
}

if (!function_exists('http_get_status')) {
    /**
     * Get HTTP status code from response
     * 
     * @param array $response Response array from Guzzle service
     * @return int|null
     */
    function http_get_status(array $response): ?int
    {
        return $response['status_code'] ?? null;
    }
}

if (!function_exists('http_get_data')) {
    /**
     * Get response data from HTTP response
     * 
     * @param array $response Response array from Guzzle service
     * @return mixed
     */
    function http_get_data(array $response)
    {
        return $response['data'] ?? null;
    }
}

if (!function_exists('http_get_headers')) {
    /**
     * Get response headers from HTTP response
     * 
     * @param array $response Response array from Guzzle service
     * @return array
     */
    function http_get_headers(array $response): array
    {
        return $response['headers'] ?? [];
    }
}
