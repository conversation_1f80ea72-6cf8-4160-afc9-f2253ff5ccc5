<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="row row-deck row-cards">
    <div class="col-12">
        <form action="<?= base_url('admin/navigation/store') ?>" method="post">
            <?= csrf_field() ?>
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Create Navigation Menu</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="mb-3">
                                <label class="form-label required">Title</label>
                                <input type="text" class="form-control <?= session('errors.title') ? 'is-invalid' : '' ?>" 
                                       name="title" value="<?= old('title') ?>" placeholder="Enter menu title" required>
                                <?php if (session('errors.title')): ?>
                                <div class="invalid-feedback"><?= session('errors.title') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">URL</label>
                                <input type="text" class="form-control <?= session('errors.url') ? 'is-invalid' : '' ?>" 
                                       name="url" value="<?= old('url') ?>" placeholder="Enter URL (optional)">
                                <small class="form-hint">Leave empty for parent menu items or use relative URLs like /admin/users</small>
                                <?php if (session('errors.url')): ?>
                                <div class="invalid-feedback"><?= session('errors.url') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Icon</label>
                                <input type="text" class="form-control <?= session('errors.icon') ? 'is-invalid' : '' ?>" 
                                       name="icon" value="<?= old('icon') ?>" placeholder="Enter icon class (optional)">
                                <small class="form-hint">Use Tabler icons like: ti ti-home, ti ti-user, etc.</small>
                                <?php if (session('errors.icon')): ?>
                                <div class="invalid-feedback"><?= session('errors.icon') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">CSS Class</label>
                                <input type="text" class="form-control <?= session('errors.css_class') ? 'is-invalid' : '' ?>" 
                                       name="css_class" value="<?= old('css_class') ?>" placeholder="Additional CSS classes (optional)">
                                <?php if (session('errors.css_class')): ?>
                                <div class="invalid-feedback"><?= session('errors.css_class') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Permission</label>
                                <input type="text" class="form-control <?= session('errors.permission') ? 'is-invalid' : '' ?>" 
                                       name="permission" value="<?= old('permission') ?>" placeholder="Permission required to view this menu (optional)">
                                <small class="form-hint">Leave empty if no permission is required</small>
                                <?php if (session('errors.permission')): ?>
                                <div class="invalid-feedback"><?= session('errors.permission') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label class="form-label">Parent Menu</label>
                                <select class="form-select <?= session('errors.parent_id') ? 'is-invalid' : '' ?>" name="parent_id">
                                    <option value="">Select Parent Menu (optional)</option>
                                    <?php foreach ($parentMenus as $parentMenu): ?>
                                    <option value="<?= $parentMenu['id'] ?>" <?= old('parent_id') == $parentMenu['id'] ? 'selected' : '' ?>>
                                        <?= esc($parentMenu['title']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (session('errors.parent_id')): ?>
                                <div class="invalid-feedback"><?= session('errors.parent_id') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Target</label>
                                <select class="form-select <?= session('errors.target') ? 'is-invalid' : '' ?>" name="target">
                                    <option value="_self" <?= old('target', '_self') == '_self' ? 'selected' : '' ?>>Same Window (_self)</option>
                                    <option value="_blank" <?= old('target') == '_blank' ? 'selected' : '' ?>>New Window (_blank)</option>
                                    <option value="_parent" <?= old('target') == '_parent' ? 'selected' : '' ?>>Parent Frame (_parent)</option>
                                    <option value="_top" <?= old('target') == '_top' ? 'selected' : '' ?>>Full Window (_top)</option>
                                </select>
                                <?php if (session('errors.target')): ?>
                                <div class="invalid-feedback"><?= session('errors.target') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Sort Order</label>
                                <input type="number" class="form-control <?= session('errors.sort_order') ? 'is-invalid' : '' ?>" 
                                       name="sort_order" value="<?= old('sort_order', 0) ?>" min="0">
                                <small class="form-hint">Lower numbers appear first</small>
                                <?php if (session('errors.sort_order')): ?>
                                <div class="invalid-feedback"><?= session('errors.sort_order') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_active" value="1" <?= old('is_active', '1') ? 'checked' : '' ?>>
                                    <span class="form-check-label">Active</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-end">
                    <div class="d-flex">
                        <a href="<?= base_url('admin/navigation') ?>" class="btn btn-link">Cancel</a>
                        <button type="submit" class="btn btn-primary ms-auto">Create Menu</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>
