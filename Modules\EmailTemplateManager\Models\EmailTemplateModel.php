<?php

namespace Modules\EmailTemplateManager\Models;

use CodeIgniter\Model;

class EmailTemplateModel extends Model
{
    protected $table = 'email_templates';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'name', 'subject', 'design_json', 'html_content', 
        'mjml_content', 'created_at', 'updated_at'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    /**
     * Get templates with pagination and search
     */
    public function getTemplates($page = 1, $perPage = 10, $search = '')
    {
        $offset = ($page - 1) * $perPage;
        
        $builder = $this->builder();
        
        if (!empty($search)) {
            $builder->like('name', $search)
                    ->orLike('subject', $search);
        }
        
        return $builder->select('id, name, subject, updated_at')
                      ->orderBy('updated_at', 'DESC')
                      ->limit($perPage, $offset)
                      ->get()
                      ->getResultArray();
    }
    
    /**
     * Get total pages for pagination
     */
    public function getTotalPages($perPage = 10, $search = '')
    {
        $builder = $this->builder();
        
        if (!empty($search)) {
            $builder->like('name', $search)
                    ->orLike('subject', $search);
        }
        
        $total = $builder->countAllResults();
        
        return ceil($total / $perPage);
    }
}