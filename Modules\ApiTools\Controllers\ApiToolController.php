<?php

namespace Modules\ApiTools\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class ApiToolController extends BaseController
{
    public function index()
    {
        
    }

    public function json_to_list()
    {
        $data = [
            'title' => 'JSON to List',
            'page_title' => 'JSON to List',
            'page_subtitle' => 'Convert JSON to List',
        ];
        return view('Modules\ApiTools\Views\api_json_form', $data);
    }
}
