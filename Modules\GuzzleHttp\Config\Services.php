<?php

namespace Modules\GuzzleHttp\Config;

use CodeIgniter\Config\BaseService;
use Mo<PERSON>les\GuzzleHttp\Services\GuzzleService;
use Mo<PERSON>les\GuzzleHttp\Config\GuzzleHttp as GuzzleConfig;

/**
 * Services Configuration for Guzzle HTTP Module
 * 
 * This class registers the Guzzle service with CodeIgniter's service container,
 * making it available throughout the application.
 */
class Services extends BaseService
{
    /**
     * Get the Guzzle HTTP service instance
     * 
     * @param array $options Additional options to override defaults
     * @param bool $getShared Whether to return a shared instance
     * @return GuzzleService
     */
    public static function guzzle(array $options = [], bool $getShared = true): GuzzleService
    {
        if ($getShared) {
            return static::getSharedInstance('guzzle', $options);
        }

        // Get the configuration
        $config = config('GuzzleHttp');
        
        // Merge configuration with any provided options
        $mergedConfig = array_merge($config->getMergedConfig(), $options);

        return new GuzzleService($mergedConfig);
    }

    /**
     * Get a Guzzle service instance configured for a specific endpoint
     * 
     * @param string $endpointName Name of the endpoint from configuration
     * @param array $options Additional options to override defaults
     * @param bool $getShared Whether to return a shared instance
     * @return GuzzleService
     * @throws \InvalidArgumentException If endpoint is not found
     */
    public static function guzzleEndpoint(string $endpointName, array $options = [], bool $getShared = true): GuzzleService
    {
        $cacheKey = "guzzle_endpoint_{$endpointName}";
        
        if ($getShared) {
            return static::getSharedInstance($cacheKey, $endpointName, $options);
        }

        $config = config('GuzzleHttp');
        $endpointConfig = $config->getEndpoint($endpointName);
        
        if ($endpointConfig === null) {
            throw new \InvalidArgumentException("Endpoint '{$endpointName}' not found in configuration");
        }

        // Merge base config with endpoint-specific config and provided options
        $mergedConfig = array_merge(
            $config->getMergedConfig(),
            $endpointConfig,
            $options
        );

        return new GuzzleService($mergedConfig);
    }

    /**
     * Get a Guzzle service instance with authentication preset
     * 
     * @param string $authPreset Name of the auth preset from configuration
     * @param array $credentials Credentials to use (username, password, token, etc.)
     * @param array $options Additional options to override defaults
     * @param bool $getShared Whether to return a shared instance
     * @return GuzzleService
     * @throws \InvalidArgumentException If auth preset is not found
     */
    public static function guzzleWithAuth(string $authPreset, array $credentials = [], array $options = [], bool $getShared = true): GuzzleService
    {
        $cacheKey = "guzzle_auth_{$authPreset}";
        
        if ($getShared) {
            return static::getSharedInstance($cacheKey, $authPreset, $credentials, $options);
        }

        $config = config('GuzzleHttp');
        $authConfig = $config->getAuthPreset($authPreset);
        
        if ($authConfig === null) {
            throw new \InvalidArgumentException("Auth preset '{$authPreset}' not found in configuration");
        }

        // Build authentication options
        $authOptions = static::buildAuthOptions($authConfig, $credentials);
        
        // Merge all configurations
        $mergedConfig = array_merge(
            $config->getMergedConfig(),
            $authOptions,
            $options
        );

        return new GuzzleService($mergedConfig);
    }

    /**
     * Build authentication options based on preset and credentials
     * 
     * @param array $authConfig
     * @param array $credentials
     * @return array
     */
    protected static function buildAuthOptions(array $authConfig, array $credentials): array
    {
        $options = [];

        switch ($authConfig['type']) {
            case 'header':
                $headerName = $authConfig['header_name'];
                $prefix = $authConfig['prefix'] ?? '';
                $value = $credentials['value'] ?? $authConfig['value'] ?? '';
                
                $options['headers'][$headerName] = $prefix . $value;
                break;

            case 'basic':
                $username = $credentials['username'] ?? $authConfig['username'] ?? '';
                $password = $credentials['password'] ?? $authConfig['password'] ?? '';
                
                $options['auth'] = [$username, $password];
                break;

            case 'digest':
                $username = $credentials['username'] ?? $authConfig['username'] ?? '';
                $password = $credentials['password'] ?? $authConfig['password'] ?? '';
                
                $options['auth'] = [$username, $password, 'digest'];
                break;

            case 'oauth':
                // OAuth implementation would go here
                // This is a placeholder for future OAuth support
                break;
        }

        return $options;
    }

    /**
     * Create a quick HTTP client for simple requests
     * This is a convenience method for one-off requests
     * 
     * @param array $options Guzzle client options
     * @return GuzzleService
     */
    public static function quickHttp(array $options = []): GuzzleService
    {
        // Don't use shared instance for quick HTTP clients
        return static::guzzle($options, false);
    }
}
