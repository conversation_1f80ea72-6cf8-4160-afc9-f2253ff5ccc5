<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<?= tabler_page_header('Menu Management', [
    'pretitle' => 'System',
    'actions' => hasPermission('menu.manage') ? tabler_button('Add New Menu', 'primary', [
        'icon' => 'ti ti-plus',
        'href' => route_to('menu.create'),
        'size' => 'sm'
    ]) : '',
    'breadcrumb' => [
        ['text' => 'Dashboard', 'url' => base_url('admin')],
        ['text' => 'Menu Management']
    ]
]) ?>

<!-- Page Body -->
<div class="page-body">
    <div class="container-xl">

        <!-- Flash Messages -->
        <?= tabler_flash_messages() ?>

        <?php if (hasPermission('menu.dashboard')) : ?>
        <!-- Statistics Cards -->
        <div class="row row-deck row-cards mb-4">
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Total Menus</div>
                        </div>
                        <div class="d-flex align-items-baseline">
                            <div class="h1 mb-0 me-2"><?= $stats['total'] ?></div>
                        </div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-primary" style="width: 100%" role="progressbar"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Active Menus</div>
                        </div>
                        <div class="d-flex align-items-baseline">
                            <div class="h1 mb-0 me-2"><?= $stats['active'] ?></div>
                        </div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-success" style="width: <?= $stats['total'] > 0 ? ($stats['active'] / $stats['total']) * 100 : 0 ?>%" role="progressbar"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Inactive Menus</div>
                        </div>
                        <div class="d-flex align-items-baseline">
                            <div class="h1 mb-0 me-2"><?= $stats['inactive'] ?></div>
                        </div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-warning" style="width: <?= $stats['total'] > 0 ? ($stats['inactive'] / $stats['total']) * 100 : 0 ?>%" role="progressbar"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-lg-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="subheader">Root Menus</div>
                        </div>
                        <div class="d-flex align-items-baseline">
                            <div class="h1 mb-0 me-2"><?= $stats['root_menus'] ?></div>
                        </div>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-info" style="width: <?= $stats['total'] > 0 ? ($stats['root_menus'] / $stats['total']) * 100 : 0 ?>%" role="progressbar"></div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Main Content Card -->
        <div class="row row-deck row-cards">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Menu Management</h3>
                        <div class="card-actions">
                            <?php if (hasPermission('menu.manage')) : ?>
                                <?= tabler_button('Add New Menu', 'primary', [
                                    'icon' => 'ti ti-plus',
                                    'href' => route_to('menu.create'),
                                    'size' => 'sm'
                                ]) ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Bulk Actions -->
                        <?php if (hasPermission('menu.manage')) : ?>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success btn-sm" id="bulk-activate" disabled>
                                        <i class="ti ti-check"></i> Activate Selected
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" id="bulk-deactivate" disabled>
                                        <i class="ti ti-x"></i> Deactivate Selected
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                                        <i class="ti ti-trash"></i> Delete Selected
                                    </button>
                                </div>
                                <span class="ms-2 text-muted" id="selected-count">0 items selected</span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- DataTable -->
                        <div class="table-responsive">
                            <table id="menuTable" class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th width="30">#</th>
                                        <?php if (hasPermission('menu.manage')) : ?>
                                        <th width="30">
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <?php endif; ?>
                                        <th>Menu Label</th>
                                        <th>URL</th>
                                        <th>Roles</th>
                                        <th>Parent</th>
                                        <th>Status</th>
                                        <th width="150">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Menu View Modal -->
<?= tabler_modal('menuViewModal', 'Menu Details', '<div id="menuViewContent"><!-- Content will be loaded here --></div>', [
    'size' => 'lg'
]) ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- DataTables -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // CSRF token
    var csrfName = '<?= csrf_token() ?>';
    var csrfHash = '<?= csrf_hash() ?>';

    // Initialize DataTable with Hermawan DataTables
    var menuTable = $('#menuTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        ajax: {
            url: '<?= site_url('admin/menu/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d[csrfName] = csrfHash;
            }
        },
        order: [['<?= hasPermission('menu.manage') ? 2 : 1 ?>', 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('menu.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'label' }
            ,{ data: 'url' }
            ,{ data: 'roles', orderable: false }
            ,{ data: 'parent_label' }
            ,{ data: 'active' }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        language: {
            processing: '<div class="d-flex justify-content-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>',
            emptyTable: 'No menus found',
            zeroRecords: 'No matching menus found'
        }
    });

    // Select all checkbox
    $('#select-all').on('click', function() {
        var checked = this.checked;
        $('.select-item').prop('checked', checked);
        updateBulkButtons();
    });

    // Individual checkbox
    $(document).on('change', '.select-item', function() {
        updateBulkButtons();

        // Update select all checkbox
        var totalItems = $('.select-item').length;
        var checkedItems = $('.select-item:checked').length;
        $('#select-all').prop('indeterminate', checkedItems > 0 && checkedItems < totalItems);
        $('#select-all').prop('checked', checkedItems === totalItems);
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        var selectedItems = $('.select-item:checked').length;
        var bulkButtons = $('#bulk-activate, #bulk-deactivate, #bulk-delete');

        if (selectedItems > 0) {
            bulkButtons.prop('disabled', false);
            $('#selected-count').text(selectedItems + ' item' + (selectedItems > 1 ? 's' : '') + ' selected');
        } else {
            bulkButtons.prop('disabled', true);
            $('#selected-count').text('0 items selected');
        }
    }

    // Bulk activate
    $('#bulk-activate').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to activate', 'warning');
            return;
        }

        Swal.fire({
            title: 'Activate Menus',
            text: 'Are you sure you want to activate ' + selectedIds.length + ' selected menu(s)?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#206bc4',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, activate!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-activate') ?>', selectedIds, 'activate');
            }
        });
    });

    // Bulk deactivate
    $('#bulk-deactivate').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to deactivate', 'warning');
            return;
        }

        Swal.fire({
            title: 'Deactivate Menus',
            text: 'Are you sure you want to deactivate ' + selectedIds.length + ' selected menu(s)?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#f59f00',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, deactivate!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-deactivate') ?>', selectedIds, 'deactivate');
            }
        });
    });

    // Bulk delete
    $('#bulk-delete').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to delete', 'warning');
            return;
        }

        Swal.fire({
            title: 'Delete Menus',
            text: 'Are you sure you want to delete ' + selectedIds.length + ' selected menu(s)? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d63384',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-delete') ?>', selectedIds, 'delete');
            }
        });
    });

    // Individual delete
    $(document).on('click', '.btn-delete', function() {
        var menuId = $(this).data('id');

        Swal.fire({
            title: 'Delete Menu',
            text: 'Are you sure you want to delete this menu? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d63384',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/menu/delete/') ?>/' + menuId;
            }
        });
    });

    // Perform bulk action
    function performBulkAction(url, ids, action) {
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                ids: ids,
                [csrfName]: csrfHash
            },
            beforeSend: function() {
                Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while we ' + action + ' the selected items.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire('Success!', response.message, 'success');
                    menuTable.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'An error occurred while processing your request.', 'error');
            }
        });
    }

    // View menu function
    window.viewMenu = function(id) {
        // Implementation for viewing menu details
        $('#menuViewModal').modal('show');
        $('#menuViewContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');

        // Load menu details via AJAX
        $.get('<?= site_url('admin/menu/view') ?>/' + id, function(data) {
            $('#menuViewContent').html(data);
        }).fail(function() {
            $('#menuViewContent').html('<div class="alert alert-danger">Failed to load menu details.</div>');
        });
    };

    // Delete menu function
    window.deleteMenu = function(id) {
        Swal.fire({
            title: 'Delete Menu',
            text: 'Are you sure you want to delete this menu? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d63384',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/menu/delete/') ?>/' + id;
            }
        });
    };
});
</script>
<?= $this->endSection() ?>