<?php

namespace Modules\MenuManagement\Models;

use CodeIgniter\Model;

class MenuModel extends Model
{
    protected $DBGroup = 'default';
    protected $table = 'menus';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'label',
        'url',
        'icon',
        'css_class',
        'target',
        'permission_id', // Keep for backward compatibility
        'parent_id',
        'sort_order',
        'active',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'label' => 'required|min_length[2]|max_length[100]',
        'url' => 'permit_empty|max_length[191]',
        'icon' => 'permit_empty|max_length[100]',
        'css_class' => 'permit_empty|max_length[100]',
        'target' => 'permit_empty|in_list[_self,_blank,_parent,_top]',
        'permission_id' => 'permit_empty|integer',
        'parent_id' => 'permit_empty|integer',
        'sort_order' => 'permit_empty|integer',
        'active' => 'permit_empty|in_list[0,1]',
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    protected $cache;
    protected $session;

    public function __construct()
    {
        parent::__construct();
        $this->cache = \Config\Services::cache();
        $this->session = \Config\Services::session();
    }

    /**
     * Get all menus with their permissions
     */
    public function getMenusWithPermissions()
    {
        return $this->select('menus.*, permissions.name as permission_name')
                    ->join('permissions', 'permissions.id = menus.permission_id', 'left')
                    ->orderBy('parent_id', 'ASC')
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get all menus with their assigned roles
     */
    public function getMenusWithRoles()
    {
        $menus = $this->select('menus.*, permissions.name as permission_name')
                     ->join('permissions', 'permissions.id = menus.permission_id', 'left')
                     ->orderBy('parent_id', 'ASC')
                     ->orderBy('sort_order', 'ASC')
                     ->findAll();

        // Get role assignments for each menu
        foreach ($menus as &$menu) {
            $roles = $this->db->table('menu_roles mr')
                             ->select('mr.role_id, r.name as role_name, r.description as role_description')
                             ->join('roles r', 'r.id = mr.role_id')
                             ->where('mr.menu_id', $menu['id'])
                             ->where('r.is_active', 1)
                             ->get()
                             ->getResultArray();

            $menu['assigned_roles'] = $roles;
            $menu['role_names'] = array_column($roles, 'role_name');
        }

        return $menus;
    }

    /**
     * Get menus with roles for DataTable builder
     */
    public function getMenusWithRolesBuilder()
    {
        return $this->db->table('menus m')
                       ->select([
                           'm.id',
                           'm.label',
                           'm.url',
                           'm.icon',
                           'm.parent_id',
                           'm.sort_order',
                           'm.active',
                           'm.created_at',
                           'm.updated_at',
                           'p.name as permission_name',
                           'pm.label as parent_label',
                           'GROUP_CONCAT(DISTINCT r.name ORDER BY r.name ASC SEPARATOR ", ") as role_names'
                       ])
                       ->join('permissions p', 'p.id = m.permission_id', 'left')
                       ->join('menus pm', 'pm.id = m.parent_id', 'left')
                       ->join('menu_roles mr', 'mr.menu_id = m.id', 'left')
                       ->join('roles r', 'r.id = mr.role_id AND r.is_active = 1', 'left')
                       ->groupBy('m.id')
                       ->orderBy('m.parent_id', 'ASC')
                       ->orderBy('m.sort_order', 'ASC');
    }

    /**
     * Get hierarchical menu structure for admin display
     */
    public function getHierarchicalMenus()
    {
        $cacheKey = 'admin_hierarchical_menus';

        // Try to get from cache first
        $menus = $this->cache->get($cacheKey);

        if ($menus === null) {
            $allMenus = $this->getMenusWithPermissions();
            $menus = $this->buildMenuTree($allMenus);

            // Cache for 2 hour
            $this->cache->save($cacheKey, $menus, 7200);
        }

        return $menus;
    }

    /**
     * Get user-accessible menu structure with role checking
     */
    public function getUserMenus($userPermissions = [], $userRoleIds = [])
    {
        // Build from database directly
        $allMenus = $this->select('menus.*, permissions.name as permission_name')
                         ->join('permissions', 'permissions.id = menus.permission_id', 'left')
                         ->where('menus.active', 1)
                         ->orderBy('parent_id', 'ASC')
                         ->orderBy('sort_order', 'ASC')
                         ->findAll();

        // Get menu role assignments
        $menuRoles = $this->db->table('menu_roles mr')
                             ->select('mr.menu_id, mr.role_id, r.name as role_name')
                             ->join('roles r', 'r.id = mr.role_id')
                             ->where('r.is_active', 1)
                             ->get()
                             ->getResultArray();

        // Group menu roles by menu_id
        $menuRoleMap = [];
        foreach ($menuRoles as $menuRole) {
            $menuRoleMap[$menuRole['menu_id']][] = $menuRole['role_id'];
        }

        // Debug: Log what we found
        log_message('debug', 'MenuModel::getUserMenus - All menus count: ' . count($allMenus));
        log_message('debug', 'MenuModel::getUserMenus - User permissions: ' . json_encode($userPermissions));
        log_message('debug', 'MenuModel::getUserMenus - User role IDs: ' . json_encode($userRoleIds));

        if (empty($allMenus)) {
            log_message('debug', 'MenuModel::getUserMenus - No menus found in database');
            return [];
        }

        $filteredMenus = $this->filterMenusByRolesAndPermissions($allMenus, $userPermissions, $userRoleIds, $menuRoleMap);
        log_message('debug', 'MenuModel::getUserMenus - Filtered menus count: ' . count($filteredMenus));

        $menus = $this->buildMenuTree($filteredMenus);
        log_message('debug', 'MenuModel::getUserMenus - Final menu tree count: ' . count($menus));

        return $menus;
    }

    /**
     * Filter menus based on user roles and permissions (hybrid approach)
     */
    private function filterMenusByRolesAndPermissions($menus, $userPermissions, $userRoleIds, $menuRoleMap)
    {
        $filtered = [];

        foreach ($menus as $menu) {
            $menuId = $menu['id'];
            $hasRoleAccess = false;
            $hasPermissionAccess = false;

            log_message('debug', 'Checking menu: ' . $menu['label'] . ' - Permission: ' . ($menu['permission_name'] ?? 'none'));

            // Check role-based access (new system)
            if (isset($menuRoleMap[$menuId])) {
                $menuRoleIds = $menuRoleMap[$menuId];
                $hasRoleAccess = !empty(array_intersect($userRoleIds, $menuRoleIds));
                log_message('debug', 'Menu "' . $menu['label'] . '" role check: ' . ($hasRoleAccess ? 'PASS' : 'FAIL'));
            }

            // Check permission-based access (legacy system)
            if (!empty($menu['permission_name'])) {
                $hasPermissionAccess = in_array($menu['permission_name'], $userPermissions);
                log_message('debug', 'Menu "' . $menu['label'] . '" permission check: ' . ($hasPermissionAccess ? 'PASS' : 'FAIL'));
            }

            // Include menu if:
            // 1. No role assignments AND no permission requirement (public menu)
            // 2. User has role access (new system)
            // 3. User has permission access (legacy system)
            if ((!isset($menuRoleMap[$menuId]) && empty($menu['permission_name'])) ||
                $hasRoleAccess ||
                $hasPermissionAccess) {
                log_message('debug', 'Menu "' . $menu['label'] . '" added');
                $filtered[] = $menu;
            } else {
                log_message('debug', 'Menu "' . $menu['label'] . '" skipped (no access)');
            }
        }

        return $filtered;
    }

    /**
     * Build hierarchical menu tree structure
     */
    private function buildMenuTree($menus, $parentId = null)
    {
        $tree = [];

        foreach ($menus as $menu) {
            if ($menu['parent_id'] == $parentId) {
                $children = $this->buildMenuTree($menus, $menu['id']);
                if (!empty($children)) {
                    $menu['children'] = $children;
                }
                $tree[] = $menu;
            }
        }

        return $tree;
    }

    /**
     * Get menu options for parent selection (excluding current menu and its children)
     */
    public function getMenuOptions($excludeId = null)
    {
        $query = $this->select('id, label, parent_id')
                      ->where('active', 1)
                      ->orderBy('parent_id', 'ASC')
                      ->orderBy('sort_order', 'ASC');

        if ($excludeId) {
            $query->where('id !=', $excludeId);
            // Also exclude children of the current menu
            $children = $this->getChildrenIds($excludeId);
            if (!empty($children)) {
                $query->whereNotIn('id', $children);
            }
        }

        $menus = $query->findAll();
        return $this->buildMenuOptions($menus);
    }

    /**
     * Get all children IDs of a menu (recursive)
     */
    public function getChildrenIds($parentId)
    {
        $children = [];
        $directChildren = $this->where('parent_id', $parentId)->findAll();

        foreach ($directChildren as $child) {
            $children[] = $child['id'];
            $grandChildren = $this->getChildrenIds($child['id']);
            $children = array_merge($children, $grandChildren);
        }

        return $children;
    }

    /**
     * Build menu options array for select dropdown
     */
    private function buildMenuOptions($menus, $parentId = null, $level = 0)
    {
        $options = [];

        foreach ($menus as $menu) {
            if ($menu['parent_id'] == $parentId) {
                $prefix = str_repeat('-- ', $level);
                $options[$menu['id']] = $prefix . $menu['label'];

                $children = $this->buildMenuOptions($menus, $menu['id'], $level + 1);
                $options = array_merge($options, $children);
            }
        }

        return $options;
    }

    /**
     * Clear all menu caches
     */
    public function clearMenuCache()
    {
        // Clear specific cache keys
        $this->cache->delete('admin_hierarchical_menus');

        // Clear user menu caches (this is a simple approach, in production you might want to be more specific)
        $this->cache->clean();

        // Clear session menu data
        $sessionData = $this->session->get();
        foreach ($sessionData as $key => $value) {
            unset($value); // Suppress unused variable warning
            if (strpos($key, 'user_menus_') === 0) {
                $this->session->remove($key);
            }
        }
    }

    /**
     * Override insert to clear cache
     */
    public function insert($data = null, bool $returnID = true)
    {
        $result = parent::insert($data, $returnID);
        if ($result) {
            $this->clearMenuCache();
        }
        return $result;
    }

    /**
     * Override update to clear cache
     */
    public function update($id = null, $data = null): bool
    {
        $result = parent::update($id, $data);
        if ($result) {
            $this->clearMenuCache();
        }
        return $result;
    }

    /**
     * Override delete to clear cache
     */
    public function delete($id = null, bool $purge = false)
    {
        $result = parent::delete($id, $purge);
        if ($result) {
            $this->clearMenuCache();
        }
        return $result;
    }

    /**
     * Update sort orders for multiple menus
     */
    public function updateSortOrders($orders)
    {
        $this->db->transStart();

        foreach ($orders as $id => $order) {
            $this->update($id, ['sort_order' => $order]);
        }

        $this->db->transComplete();

        if ($this->db->transStatus()) {
            $this->clearMenuCache();
            return true;
        }

        return false;
    }
}